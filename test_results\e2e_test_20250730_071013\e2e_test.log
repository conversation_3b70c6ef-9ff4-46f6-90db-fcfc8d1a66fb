2025-07-30 07:10:13 - rl_portfolio_rebalancing - INFO - setup_logging:129 - Logging to rotating file: test_results\e2e_test_20250730_071013\e2e_test.log (max 50MB, 5 backups)
2025-07-30 07:10:13 - rl_portfolio_rebalancing - DEBUG - setup_logging:147 - Memory handler added for critical error buffering
2025-07-30 07:10:13 - rl_portfolio_rebalancing - DEBUG - setup_logging:155 - Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-07-30 07:10:13 - rl_portfolio_rebalancing - DEBUG - setup_logging:156 - Platform: win32
2025-07-30 07:10:13 - rl_portfolio_rebalancing - DEBUG - setup_logging:157 - Working directory: C:\Users\<USER>\Desktop\RL assets
2025-07-30 07:10:13 - rl_portfolio_rebalancing - DEBUG - setup_logging:158 - Process ID: 16580
2025-07-30 07:10:13 - e2e_unicode_test - INFO - __init__:109 -    Test session directory: test_results\e2e_test_20250730_071013
2025-07-30 07:10:13 - e2e_unicode_test - DEBUG - detect_console_encoding:82 - Starting console encoding detection
2025-07-30 07:10:13 - e2e_unicode_test - DEBUG - _get_console_encoding:161 - Selected console encoding: utf-8 from candidates: ['utf-8', 'utf-8', 'cp950', 'utf-8', 'utf-8']
2025-07-30 07:10:13 - e2e_unicode_test - DEBUG - _test_utf8_support:184 - UTF-8 encoding/decoding test passed
2025-07-30 07:10:13 - e2e_unicode_test - DEBUG - _get_console_encoding:161 - Selected console encoding: utf-8 from candidates: ['utf-8', 'utf-8', 'cp950', 'utf-8', 'utf-8']
2025-07-30 07:10:13 - e2e_unicode_test - DEBUG - _test_unicode_support:215 - Unicode support test passed for encoding: utf-8
2025-07-30 07:10:13 - e2e_unicode_test - INFO - detect_console_encoding:115 - Encoding detection completed: utf-8 (UTF-8: True, Unicode: True, Recommended: unicode, Confidence: 0.90)
2025-07-30 07:10:13 - e2e_unicode_test - DEBUG - __init__:588 - CharacterMapper initialized with mode: auto, total mappings: 166
2025-07-30 07:10:13 - e2e_unicode_test - DEBUG - map_unicode_to_safe:618 - Applied 4 character replacements in text
2025-07-30 07:10:13 - e2e_unicode_test - DEBUG - map_unicode_to_safe:618 - Applied 4 character replacements in text
