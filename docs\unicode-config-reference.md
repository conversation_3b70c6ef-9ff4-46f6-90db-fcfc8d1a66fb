# Unicode Logging Configuration Reference

## Quick Configuration Guide

### Basic Configurations

#### Auto-Detection (Recommended)
```python
UNICODE_LOGGING = {
    'mode': 'auto'
}
```
- ✅ Works in all environments
- ✅ Automatic fallback on errors
- ✅ No manual configuration needed

#### Force ASCII Mode
```python
UNICODE_LOGGING = {
    'mode': 'ascii'
}
```
- ✅ Maximum compatibility
- ✅ No encoding errors
- ❌ No Unicode characters

#### Force Unicode Mode
```python
UNICODE_LOGGING = {
    'mode': 'unicode'
}
```
- ✅ Full Unicode support
- ❌ May cause errors on incompatible systems
- ⚠️ Use only when Unicode support is confirmed

## Configuration Parameters

### `mode` (string)
**Default**: `'auto'`  
**Options**: `'auto'`, `'unicode'`, `'ascii'`

Controls the character set used for logging output.

```python
# Automatic detection (recommended)
'mode': 'auto'

# Force Unicode characters
'mode': 'unicode'

# Force ASCII characters only
'mode': 'ascii'
```

### `fallback_on_error` (boolean)
**Default**: `True`  
**Recommended**: `True`

Enables automatic fallback to ASCII mode when encoding errors occur.

```python
# Enable fallback (recommended)
'fallback_on_error': True

# Disable fallback (may cause crashes)
'fallback_on_error': False
```

### `debug_encoding_issues` (boolean)
**Default**: `False`  
**Use for**: Troubleshooting

Enables detailed debug logging for encoding detection and errors.

```python
# Enable debug output
'debug_encoding_issues': True

# Disable debug output (production)
'debug_encoding_issues': False
```

### `custom_mappings` (dict)
**Default**: `{}`  
**Format**: `{'unicode_char': 'ascii_replacement'}`

Defines custom Unicode to ASCII character mappings.

```python
'custom_mappings': {
    '🚀': '[LAUNCH]',
    '💡': '[IDEA]',
    '🔥': '[HOT]',
    '❄️': '[COLD]'
}
```

## Environment Variable Overrides

Override configuration using environment variables:

### Windows Command Prompt
```cmd
set UNICODE_LOGGING_MODE=ascii
set UNICODE_LOGGING_DEBUG=true
set UNICODE_LOGGING_FALLBACK=false
```

### Windows PowerShell
```powershell
$env:UNICODE_LOGGING_MODE = "ascii"
$env:UNICODE_LOGGING_DEBUG = "true"
$env:UNICODE_LOGGING_FALLBACK = "false"
```

### Environment Variable Reference
| Variable | Values | Default | Description |
|----------|--------|---------|-------------|
| `UNICODE_LOGGING_MODE` | `auto`, `unicode`, `ascii` | `auto` | Logging mode |
| `UNICODE_LOGGING_DEBUG` | `true`, `false` | `false` | Debug output |
| `UNICODE_LOGGING_FALLBACK` | `true`, `false` | `true` | Error fallback |

## Common Configuration Patterns

### Development Environment
```python
UNICODE_LOGGING = {
    'mode': 'auto',
    'fallback_on_error': True,
    'debug_encoding_issues': True,  # Enable for troubleshooting
    'custom_mappings': {
        '🐛': '[BUG]',
        '🔧': '[FIX]',
        '🧪': '[TEST]'
    }
}
```

### Production Environment
```python
UNICODE_LOGGING = {
    'mode': 'auto',
    'fallback_on_error': True,
    'debug_encoding_issues': False,  # Disable for performance
    'custom_mappings': {}
}
```

### CI/CD Environment
```python
UNICODE_LOGGING = {
    'mode': 'ascii',  # Force ASCII for reliability
    'fallback_on_error': False,  # Fail fast on issues
    'debug_encoding_issues': False,
    'custom_mappings': {}
}
```

### High-Performance Environment
```python
UNICODE_LOGGING = {
    'mode': 'ascii',  # Skip encoding detection
    'fallback_on_error': False,  # Skip error handling
    'debug_encoding_issues': False,  # No debug overhead
    'custom_mappings': {
        # Only essential mappings
        '✅': '[OK]',
        '❌': '[FAIL]'
    }
}
```

### Multilingual Environment
```python
UNICODE_LOGGING = {
    'mode': 'auto',
    'fallback_on_error': True,
    'debug_encoding_issues': False,
    'custom_mappings': {
        # Language-appropriate fallbacks
        '✅': '[成功]',  # Chinese
        '❌': '[失敗]',  # Chinese
        '⚠️': '[警告]'   # Chinese
    }
}
```

## Character Mapping Examples

### Trading/Financial Mappings
```python
'custom_mappings': {
    '💰': '[PROFIT]',
    '💸': '[LOSS]',
    '📈': '[BULL]',
    '📉': '[BEAR]',
    '⚡': '[FAST]',
    '🎯': '[TARGET]',
    '⚖️': '[BALANCE]',
    '🏆': '[WINNER]'
}
```

### System/Technical Mappings
```python
'custom_mappings': {
    '🔧': '[CONFIG]',
    '🔍': '[SEARCH]',
    '📝': '[LOG]',
    '🚨': '[ALERT]',
    '⚙️': '[SYSTEM]',
    '🔒': '[SECURE]',
    '🔓': '[UNLOCK]',
    '🌐': '[NETWORK]'
}
```

### Status/Progress Mappings
```python
'custom_mappings': {
    '🟢': '[ONLINE]',
    '🔴': '[OFFLINE]',
    '🟡': '[PENDING]',
    '⏳': '[WAITING]',
    '✨': '[NEW]',
    '🔄': '[REFRESH]',
    '⏸️': '[PAUSE]',
    '▶️': '[PLAY]'
}
```

## Validation and Testing

### Configuration Validation
```python
# Test your configuration
from unicode_logging import setup_unicode_logging, UnicodeLoggingConfig

config = UnicodeLoggingConfig(
    mode='auto',
    fallback_on_error=True,
    custom_mappings={'✅': '[OK]'}
)

# This will validate the configuration
setup_unicode_logging(config)
```

### Character Mapping Test
```python
# Test character mappings
from unicode_logging import CharacterMapper

mapper = CharacterMapper(mode='auto')
test_chars = ['✅', '❌', '⚠️', '🎯', '🚀']

for char in test_chars:
    replacement = mapper.get_replacement(char)
    print(f"{char} -> {replacement}")
```

### Console Encoding Test
```python
# Test console encoding detection
from unicode_logging import EncodingDetector

detector = EncodingDetector()
info = detector.detect_console_encoding()

print(f"Console encoding: {info.console_encoding}")
print(f"Supports Unicode: {info.supports_unicode}")
print(f"Recommended mode: {info.recommended_mode}")
```

## Troubleshooting Configuration Issues

### Issue: Configuration Not Applied
**Check**: Configuration loading
```python
from config import UNICODE_LOGGING
print(f"Loaded config: {UNICODE_LOGGING}")
```

### Issue: Custom Mappings Not Working
**Check**: Mapping format
```python
# Correct format
'custom_mappings': {
    '✅': '[OK]',  # Unicode string -> ASCII string
    '❌': '[FAIL]'
}

# Incorrect format
'custom_mappings': {
    'checkmark': '[OK]',  # Wrong: not Unicode character
    '✅': ['OK']          # Wrong: list instead of string
}
```

### Issue: Environment Variables Ignored
**Check**: Variable names and values
```cmd
# Check current values
echo %UNICODE_LOGGING_MODE%
echo %UNICODE_LOGGING_DEBUG%

# Set correct values
set UNICODE_LOGGING_MODE=ascii
set UNICODE_LOGGING_DEBUG=true
```

## Performance Considerations

### Configuration Impact on Performance

| Setting | Performance Impact | Recommendation |
|---------|-------------------|----------------|
| `mode: 'auto'` | Low (one-time detection) | ✅ Recommended |
| `mode: 'ascii'` | Minimal | ✅ For high-performance |
| `mode: 'unicode'` | Minimal | ⚠️ Only if Unicode confirmed |
| `debug_encoding_issues: true` | Medium | ❌ Development only |
| `fallback_on_error: true` | Low | ✅ Recommended |
| Large `custom_mappings` | Low-Medium | ⚠️ Use only needed mappings |

### Optimization Tips
1. **Use ASCII mode** for maximum performance
2. **Disable debug mode** in production
3. **Minimize custom mappings** to only essential characters
4. **Enable caching** (automatic in the system)
5. **Test performance** in your specific environment

## Migration Guide

### From No Unicode Logging
```python
# Before (no Unicode logging)
# Just regular logging configuration

# After (with Unicode logging)
UNICODE_LOGGING = {
    'mode': 'auto',
    'fallback_on_error': True
}
```

### From Manual Character Replacement
```python
# Before (manual replacement in code)
logger.info("[OK] Task completed")  # Manual ASCII

# After (automatic replacement)
logger.info("✅ Task completed")    # Automatic Unicode/ASCII
```

### From Environment-Specific Code
```python
# Before (environment-specific code)
import platform
if platform.system() == 'Windows':
    logger.info("[OK] Windows message")
else:
    logger.info("✅ Unix message")

# After (automatic handling)
logger.info("✅ Cross-platform message")  # Works everywhere
```