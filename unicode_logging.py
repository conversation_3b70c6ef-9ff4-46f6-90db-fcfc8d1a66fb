"""
Unicode-safe logging utilities for RL Portfolio Rebalancing System

This module provides encoding detection and character mapping functionality
to ensure reliable logging across different Windows locale configurations.
"""

import sys
import locale
import codecs
import io
import time
import threading
from typing import Dict, Optional, Tuple, List, TYPE_CHECKING

if TYPE_CHECKING:
    from typing import Any
from dataclasses import dataclass, field
from enum import Enum
import logging
from functools import lru_cache
from collections import deque, defaultdict


@dataclass
class CharacterMapping:
    """Configuration for mapping a Unicode character to ASCII equivalent."""
    unicode_char: str
    ascii_replacement: str
    context_sensitive: bool = False
    visual_weight: int = 1  # For maintaining hierarchy


class LoggingMode(Enum):
    """Enumeration of available logging modes."""
    AUTO = "auto"
    UNICODE = "unicode"
    ASCII = "ascii"
    SAFE = "safe"  # ASCII with enhanced visual markers


@dataclass
class EncodingInfo:
    """Information about console encoding capabilities."""
    console_encoding: str
    supports_utf8: bool
    supports_unicode: bool
    recommended_mode: LoggingMode
    detection_confidence: float
    fallback_encoding: str


class EncodingDetector:
    """
    Detects console encoding capabilities and provides recommendations
    for optimal logging mode selection.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the encoding detector.
        
        Args:
            logger: Optional logger instance for debug output
        """
        self.logger = logger or logging.getLogger('unicode_logging.detector')
        self._cached_info: Optional[EncodingInfo] = None
        self._cache_valid = False
    
    @lru_cache(maxsize=1)
    def detect_console_encoding(self) -> EncodingInfo:
        """
        Detect the console's encoding capabilities and provide recommendations.
        
        Returns:
            EncodingInfo object with detection results and recommendations
        """
        if self._cached_info and self._cache_valid:
            self.logger.debug("Using cached encoding detection results")
            return self._cached_info
        
        self.logger.debug("Starting console encoding detection")
        
        # Get console encoding information
        console_encoding = self._get_console_encoding()
        supports_utf8 = self._test_utf8_support()
        supports_unicode = self._test_unicode_support()
        
        # Determine recommended mode based on capabilities
        recommended_mode = self._determine_recommended_mode(
            console_encoding, supports_utf8, supports_unicode
        )
        
        # Calculate confidence based on test results
        confidence = self._calculate_confidence(
            console_encoding, supports_utf8, supports_unicode
        )
        
        # Determine fallback encoding
        fallback_encoding = self._get_fallback_encoding(console_encoding)
        
        encoding_info = EncodingInfo(
            console_encoding=console_encoding,
            supports_utf8=supports_utf8,
            supports_unicode=supports_unicode,
            recommended_mode=recommended_mode,
            detection_confidence=confidence,
            fallback_encoding=fallback_encoding
        )
        
        # Cache the results
        self._cached_info = encoding_info
        self._cache_valid = True
        
        self.logger.info(f"Encoding detection completed: {console_encoding} "
                        f"(UTF-8: {supports_utf8}, Unicode: {supports_unicode}, "
                        f"Recommended: {recommended_mode.value}, Confidence: {confidence:.2f})")
        
        return encoding_info
    
    def _get_console_encoding(self) -> str:
        """
        Get the console's encoding using multiple detection methods.
        
        Returns:
            String representation of the console encoding
        """
        # Try multiple methods to detect console encoding
        encoding_candidates = []
        
        # Method 1: stdout encoding
        if hasattr(sys.stdout, 'encoding') and sys.stdout.encoding:
            encoding_candidates.append(sys.stdout.encoding)
        
        # Method 2: stderr encoding
        if hasattr(sys.stderr, 'encoding') and sys.stderr.encoding:
            encoding_candidates.append(sys.stderr.encoding)
        
        # Method 3: locale encoding
        try:
            locale_encoding = locale.getpreferredencoding()
            if locale_encoding:
                encoding_candidates.append(locale_encoding)
        except Exception as e:
            self.logger.debug(f"Failed to get locale encoding: {e}")
        
        # Method 4: system default encoding
        encoding_candidates.append(sys.getdefaultencoding())
        
        # Method 5: filesystem encoding
        if hasattr(sys, 'getfilesystemencoding'):
            fs_encoding = sys.getfilesystemencoding()
            if fs_encoding:
                encoding_candidates.append(fs_encoding)
        
        # Find the most common encoding or use the first valid one
        if encoding_candidates:
            # Use the first non-None encoding
            for encoding in encoding_candidates:
                if encoding and encoding.lower() != 'none':
                    self.logger.debug(f"Selected console encoding: {encoding} from candidates: {encoding_candidates}")
                    return encoding.lower()
        
        # Fallback to utf-8 if nothing else works
        self.logger.warning("Could not detect console encoding, defaulting to utf-8")
        return 'utf-8'
    
    def _test_utf8_support(self) -> bool:
        """
        Test if the console supports UTF-8 encoding.
        
        Returns:
            True if UTF-8 is supported, False otherwise
        """
        try:
            # Test by attempting to encode/decode UTF-8 characters
            test_string = "Test UTF-8: ✓ ✗ ★ ♦"
            
            # Try encoding to bytes and back
            encoded = test_string.encode('utf-8')
            decoded = encoded.decode('utf-8')
            
            if decoded == test_string:
                self.logger.debug("UTF-8 encoding/decoding test passed")
                return True
            else:
                self.logger.debug("UTF-8 encoding/decoding test failed: string mismatch")
                return False
                
        except (UnicodeEncodeError, UnicodeDecodeError, LookupError) as e:
            self.logger.debug(f"UTF-8 support test failed: {e}")
            return False
        except Exception as e:
            self.logger.debug(f"Unexpected error in UTF-8 test: {e}")
            return False
    
    def _test_unicode_support(self) -> bool:
        """
        Test if the console can display Unicode characters.
        
        Returns:
            True if Unicode characters can be displayed, False otherwise
        """
        # Get console encoding
        console_encoding = self._get_console_encoding()
        
        # Test characters commonly used in the logging system
        test_chars = ['✅', '❌', '🎯', '💻', '🚀', '⚠️', '📊', '📈']
        
        try:
            for char in test_chars:
                # Try to encode the character with the console encoding
                char.encode(console_encoding)
            
            self.logger.debug(f"Unicode support test passed for encoding: {console_encoding}")
            return True
            
        except (UnicodeEncodeError, LookupError) as e:
            self.logger.debug(f"Unicode support test failed for {console_encoding}: {e}")
            return False
        except Exception as e:
            self.logger.debug(f"Unexpected error in Unicode test: {e}")
            return False
    
    def _determine_recommended_mode(self, console_encoding: str, supports_utf8: bool, supports_unicode: bool) -> LoggingMode:
        """
        Determine the recommended logging mode based on encoding capabilities.
        
        Args:
            console_encoding: Detected console encoding
            supports_utf8: Whether UTF-8 is supported
            supports_unicode: Whether Unicode characters are supported
        
        Returns:
            Recommended LoggingMode
        """
        # If both UTF-8 and Unicode are supported, recommend Unicode mode
        if supports_utf8 and supports_unicode:
            return LoggingMode.UNICODE
        
        # If UTF-8 is supported but Unicode display fails, use safe mode
        if supports_utf8 and not supports_unicode:
            return LoggingMode.SAFE
        
        # Check for specific problematic encodings
        problematic_encodings = ['cp950', 'cp936', 'cp932', 'cp949', 'cp1252']
        if any(encoding in console_encoding.lower() for encoding in problematic_encodings):
            return LoggingMode.ASCII
        
        # For unknown or limited encodings, use ASCII mode
        if not supports_utf8 and not supports_unicode:
            return LoggingMode.ASCII
        
        # Default to AUTO mode for edge cases
        return LoggingMode.AUTO
    
    def _calculate_confidence(self, console_encoding: str, supports_utf8: bool, supports_unicode: bool) -> float:
        """
        Calculate confidence level in the encoding detection results.
        
        Args:
            console_encoding: Detected console encoding
            supports_utf8: Whether UTF-8 is supported
            supports_unicode: Whether Unicode characters are supported
        
        Returns:
            Confidence level between 0.0 and 1.0
        """
        confidence = 0.5  # Base confidence
        
        # Increase confidence for well-known encodings
        known_encodings = ['utf-8', 'cp1252', 'cp950', 'ascii', 'latin-1']
        if any(encoding in console_encoding.lower() for encoding in known_encodings):
            confidence += 0.2
        
        # Increase confidence if tests are consistent
        if supports_utf8 and supports_unicode:
            confidence += 0.2  # Both tests passed
        elif not supports_utf8 and not supports_unicode:
            confidence += 0.2  # Both tests failed consistently
        else:
            confidence += 0.1  # Mixed results, lower confidence boost
        
        # Increase confidence for Windows-specific encodings on Windows
        if sys.platform.startswith('win'):
            windows_encodings = ['cp1252', 'cp950', 'cp936', 'cp932', 'cp949']
            if any(encoding in console_encoding.lower() for encoding in windows_encodings):
                confidence += 0.1
        
        return min(confidence, 1.0)  # Cap at 1.0
    
    def _get_fallback_encoding(self, console_encoding: str) -> str:
        """
        Determine appropriate fallback encoding.
        
        Args:
            console_encoding: Primary console encoding
        
        Returns:
            Fallback encoding string
        """
        # For UTF-8, fallback to latin-1 which can handle most characters
        if 'utf' in console_encoding.lower():
            return 'latin-1'
        
        # For Windows code pages, fallback to cp1252 (Western European)
        if console_encoding.lower().startswith('cp'):
            return 'cp1252'
        
        # For other encodings, fallback to ascii
        return 'ascii'
    
    def test_unicode_support(self) -> bool:
        """
        Public method to test Unicode support.
        
        Returns:
            True if Unicode characters can be safely displayed
        """
        encoding_info = self.detect_console_encoding()
        return encoding_info.supports_unicode
    
    def get_recommended_mode(self) -> LoggingMode:
        """
        Get the recommended logging mode for the current console.
        
        Returns:
            Recommended LoggingMode
        """
        encoding_info = self.detect_console_encoding()
        return encoding_info.recommended_mode
    
    def is_utf8_capable(self) -> bool:
        """
        Check if the console is UTF-8 capable.
        
        Returns:
            True if UTF-8 encoding is supported
        """
        encoding_info = self.detect_console_encoding()
        return encoding_info.supports_utf8
    
    def invalidate_cache(self) -> None:
        """
        Invalidate the cached encoding detection results.
        Useful when console settings might have changed.
        """
        self._cache_valid = False
        self._cached_info = None
        self.logger.debug("Encoding detection cache invalidated")
    
    def get_detection_summary(self) -> Dict[str, any]:
        """
        Get a summary of the encoding detection results for debugging.
        
        Returns:
            Dictionary with detection results and system information
        """
        encoding_info = self.detect_console_encoding()
        
        return {
            'console_encoding': encoding_info.console_encoding,
            'supports_utf8': encoding_info.supports_utf8,
            'supports_unicode': encoding_info.supports_unicode,
            'recommended_mode': encoding_info.recommended_mode.value,
            'detection_confidence': encoding_info.detection_confidence,
            'fallback_encoding': encoding_info.fallback_encoding,
            'platform': sys.platform,
            'python_version': sys.version,
            'stdout_encoding': getattr(sys.stdout, 'encoding', 'unknown'),
            'stderr_encoding': getattr(sys.stderr, 'encoding', 'unknown'),
            'locale_encoding': locale.getpreferredencoding() if hasattr(locale, 'getpreferredencoding') else 'unknown'
        }


class CharacterMapper:
    """
    Maps Unicode characters to ASCII equivalents while maintaining visual hierarchy.
    
    This class provides context-aware character replacement functionality to ensure
    that log messages remain readable and maintain their visual structure when
    Unicode characters cannot be displayed.
    """
    
    # Default character mapping table for common emoji used in the system
    DEFAULT_MAPPINGS = {
        # Success/Status indicators
        '✅': '[OK]',
        '✓': '[OK]',
        '❌': '[FAIL]',
        '✗': '[FAIL]',
        '⚠️': '[WARNING]',
        '⚠': '[WARNING]',
        
        # Progress and milestone indicators
        '🎯': '[MILESTONE]',
        '🚀': '[PHASE]',
        '📊': '[DATA]',
        '📈': '[METRICS]',
        '📉': '[DECLINE]',
        '💻': '[RESOURCE]',
        '🏥': '[HEALTH]',
        '🔢': '[COUNT]',
        '💰': '[MONEY]',
        '📋': '[LIST]',
        '🔍': '[SEARCH]',
        '⏰': '[TIME]',
        '📅': '[DATE]',
        '🔧': '[TOOL]',
        '⚙️': '[CONFIG]',
        '🔄': '[REFRESH]',
        '💾': '[SAVE]',
        '📁': '[FOLDER]',
        '📄': '[FILE]',
        '🌐': '[WEB]',
        '🔗': '[LINK]',
        '🔒': '[SECURE]',
        '🔓': '[UNLOCK]',
        '🎨': '[STYLE]',
        '🧪': '[TEST]',
        '🐛': '[BUG]',
        '🔥': '[HOT]',
        '❄️': '[COLD]',
        '⭐': '[STAR]',
        '🏆': '[TROPHY]',
        '🎉': '[CELEBRATE]',
        '💡': '[IDEA]',
        '🔔': '[ALERT]',
        '📢': '[ANNOUNCE]',
        '🎵': '[MUSIC]',
        '🎮': '[GAME]',
        '🚨': '[EMERGENCY]',
        '🛠️': '[REPAIR]',
        '📦': '[PACKAGE]',
        '🌟': '[HIGHLIGHT]',
        '🔮': '[PREDICT]',
        '📝': '[NOTE]',
        '✨': '[SPARKLE]',
        '🎪': '[EVENT]',
        '🎭': '[MASK]',
        '🎬': '[ACTION]',
        '🎤': '[MIC]',
        '🎧': '[AUDIO]',
        '📺': '[VIDEO]',
        '📷': '[CAMERA]',
        '🖼️': '[IMAGE]',
        '🗂️': '[ORGANIZE]',
        '📚': '[BOOKS]',
        '📖': '[READ]',
        '✏️': '[EDIT]',
        '🖊️': '[PEN]',
        '🖍️': '[CRAYON]',
        '📐': '[RULER]',
        '📏': '[MEASURE]',
        '🔬': '[SCIENCE]',
        '🧬': '[DNA]',
        '⚗️': '[CHEMISTRY]',
        '🔭': '[TELESCOPE]',
        '🗺️': '[MAP]',
        '🧭': '[COMPASS]',
        '⛰️': '[MOUNTAIN]',
        '🏔️': '[PEAK]',
        '🌊': '[WAVE]',
        '🔋': '[BATTERY]',
        '⚡': '[POWER]',
        '🌈': '[RAINBOW]',
        '☀️': '[SUN]',
        '🌙': '[MOON]',
        '⭐': '[STAR]',
        '🌍': '[EARTH]',
        '🌎': '[GLOBE]',
        '🌏': '[WORLD]',
        
        # Mathematical and technical symbols
        '∞': '[INFINITY]',
        '≈': '[APPROX]',
        '≠': '[NOT_EQUAL]',
        '≤': '[LESS_EQUAL]',
        '≥': '[GREATER_EQUAL]',
        '±': '[PLUS_MINUS]',
        '×': '[MULTIPLY]',
        '÷': '[DIVIDE]',
        '√': '[SQRT]',
        '∑': '[SUM]',
        '∆': '[DELTA]',
        'π': '[PI]',
        '°': '[DEGREE]',
        '→': '[ARROW_RIGHT]',
        '←': '[ARROW_LEFT]',
        '↑': '[ARROW_UP]',
        '↓': '[ARROW_DOWN]',
        '↔': '[ARROW_BOTH]',
        '⇒': '[IMPLIES]',
        '⇔': '[EQUIVALENT]',
        '∈': '[IN]',
        '∉': '[NOT_IN]',
        '⊂': '[SUBSET]',
        '⊃': '[SUPERSET]',
        '∪': '[UNION]',
        '∩': '[INTERSECTION]',
        '∅': '[EMPTY_SET]',
        
        # Currency symbols
        '€': '[EUR]',
        '£': '[GBP]',
        '¥': '[YEN]',
        '₹': '[INR]',
        '₽': '[RUB]',
        '₩': '[KRW]',
        '₪': '[ILS]',
        '₫': '[VND]',
        '₦': '[NGN]',
        '₡': '[CRC]',
        '₨': '[PKR]',
        '₱': '[PHP]',
        '₴': '[UAH]',
        '₸': '[KZT]',
        '₼': '[AZN]',
        '₾': '[GEL]',
        
        # Box drawing and borders (for tables and layouts)
        '│': '|',
        '─': '-',
        '┌': '+',
        '┐': '+',
        '└': '+',
        '┘': '+',
        '├': '+',
        '┤': '+',
        '┬': '+',
        '┴': '+',
        '┼': '+',
        '║': '||',
        '═': '==',
        '╔': '++',
        '╗': '++',
        '╚': '++',
        '╝': '++',
        '╠': '++',
        '╣': '++',
        '╦': '++',
        '╩': '++',
        '╬': '++',
        
        # Quotation marks and punctuation
        '\u201c': '"',  # Left double quotation mark
        '\u201d': '"',  # Right double quotation mark
        '\u2018': "'",  # Left single quotation mark
        '\u2019': "'",  # Right single quotation mark
        '…': '...',
        '–': '-',
        '—': '--',
        '•': '*',
        '◦': 'o',
        '▪': '*',
        '▫': 'o',
        '■': '[BLOCK]',
        '□': '[SQUARE]',
        '▲': '[TRIANGLE]',
        '△': '[TRIANGLE_OUTLINE]',
        '●': '[CIRCLE]',
        '○': '[CIRCLE_OUTLINE]',
        '◆': '[DIAMOND]',
        '◇': '[DIAMOND_OUTLINE]',
    }
    
    def __init__(self, mode: LoggingMode, custom_mappings: Optional[Dict[str, str]] = None, logger: Optional[logging.Logger] = None):
        """
        Initialize the character mapper.
        
        Args:
            mode: Current logging mode
            custom_mappings: Optional custom character mappings to override defaults
            logger: Optional logger instance for debug output
        """
        self.mode = mode
        self.logger = logger or logging.getLogger('unicode_logging.mapper')
        
        # Build the complete mapping table
        self._mappings = self.DEFAULT_MAPPINGS.copy()
        if custom_mappings:
            self._mappings.update(custom_mappings)
            self.logger.debug(f"Added {len(custom_mappings)} custom character mappings")
        
        # Create reverse mapping for debugging
        self._reverse_mappings = {v: k for k, v in self._mappings.items()}
        
        self.logger.debug(f"CharacterMapper initialized with mode: {mode.value}, "
                         f"total mappings: {len(self._mappings)}")
    
    def map_unicode_to_safe(self, text: str) -> str:
        """
        Map Unicode characters in text to ASCII-safe equivalents.
        
        Args:
            text: Input text that may contain Unicode characters
            
        Returns:
            Text with Unicode characters replaced by ASCII equivalents
        """
        if not text:
            return text
        
        # In Unicode mode, return text as-is
        if self.mode == LoggingMode.UNICODE:
            return text
        
        # For other modes, apply character mapping
        result = text
        replacements_made = 0
        
        for unicode_char, ascii_replacement in self._mappings.items():
            if unicode_char in result:
                result = result.replace(unicode_char, ascii_replacement)
                replacements_made += 1
        
        if replacements_made > 0:
            self.logger.debug(f"Applied {replacements_made} character replacements in text")
        
        return result
    
    def get_replacement(self, unicode_char: str) -> str:
        """
        Get the ASCII replacement for a specific Unicode character.
        
        Args:
            unicode_char: Unicode character to replace
            
        Returns:
            ASCII replacement string, or the original character if no mapping exists
        """
        if self.mode == LoggingMode.UNICODE:
            return unicode_char
        
        replacement = self._mappings.get(unicode_char, unicode_char)
        
        if replacement != unicode_char:
            self.logger.debug(f"Mapped '{unicode_char}' to '{replacement}'")
        
        return replacement
    
    def update_mode(self, mode: LoggingMode) -> None:
        """
        Update the logging mode for this mapper.
        
        Args:
            mode: New logging mode to use
        """
        old_mode = self.mode
        self.mode = mode
        self.logger.info(f"CharacterMapper mode updated from {old_mode.value} to {mode.value}")
    
    def add_custom_mapping(self, unicode_char: str, ascii_replacement: str) -> None:
        """
        Add or update a custom character mapping.
        
        Args:
            unicode_char: Unicode character to map
            ascii_replacement: ASCII replacement string
        """
        old_replacement = self._mappings.get(unicode_char)
        self._mappings[unicode_char] = ascii_replacement
        
        # Update reverse mapping
        if old_replacement and old_replacement in self._reverse_mappings:
            del self._reverse_mappings[old_replacement]
        self._reverse_mappings[ascii_replacement] = unicode_char
        
        if old_replacement:
            self.logger.debug(f"Updated mapping for '{unicode_char}': '{old_replacement}' -> '{ascii_replacement}'")
        else:
            self.logger.debug(f"Added new mapping: '{unicode_char}' -> '{ascii_replacement}'")
    
    def remove_mapping(self, unicode_char: str) -> bool:
        """
        Remove a character mapping.
        
        Args:
            unicode_char: Unicode character to remove mapping for
            
        Returns:
            True if mapping was removed, False if it didn't exist
        """
        if unicode_char in self._mappings:
            ascii_replacement = self._mappings[unicode_char]
            del self._mappings[unicode_char]
            
            if ascii_replacement in self._reverse_mappings:
                del self._reverse_mappings[ascii_replacement]
            
            self.logger.debug(f"Removed mapping for '{unicode_char}' -> '{ascii_replacement}'")
            return True
        
        return False
    
    def get_all_mappings(self) -> Dict[str, str]:
        """
        Get a copy of all current character mappings.
        
        Returns:
            Dictionary of Unicode character to ASCII replacement mappings
        """
        return self._mappings.copy()
    
    def get_mapping_count(self) -> int:
        """
        Get the total number of character mappings.
        
        Returns:
            Number of character mappings currently configured
        """
        return len(self._mappings)
    
    def has_mapping(self, unicode_char: str) -> bool:
        """
        Check if a mapping exists for a Unicode character.
        
        Args:
            unicode_char: Unicode character to check
            
        Returns:
            True if mapping exists, False otherwise
        """
        return unicode_char in self._mappings
    
    def test_mapping(self, text: str) -> Tuple[str, int]:
        """
        Test character mapping on text and return statistics.
        
        Args:
            text: Text to test mapping on
            
        Returns:
            Tuple of (mapped_text, number_of_replacements)
        """
        if not text:
            return text, 0
        
        original_mode = self.mode
        # Temporarily set to ASCII mode to force mapping
        self.mode = LoggingMode.ASCII
        
        mapped_text = self.map_unicode_to_safe(text)
        replacements = sum(1 for char in text if char in self._mappings)
        
        # Restore original mode
        self.mode = original_mode
        
        return mapped_text, replacements
    
    def get_context_aware_replacement(self, unicode_char: str, context: str = "") -> str:
        """
        Get context-aware replacement for a Unicode character.
        
        This method can provide different replacements based on the surrounding context,
        allowing for more intelligent character substitution.
        
        Args:
            unicode_char: Unicode character to replace
            context: Surrounding text context for intelligent replacement
            
        Returns:
            Context-appropriate ASCII replacement
        """
        # Get base replacement
        base_replacement = self.get_replacement(unicode_char)
        
        # For now, return base replacement
        # Future enhancement: implement context-sensitive logic
        # For example, different replacements based on log level, message type, etc.
        
        return base_replacement
    
    def validate_mappings(self) -> Dict[str, str]:
        """
        Validate all character mappings and return any issues found.
        
        Returns:
            Dictionary of validation issues (empty if all mappings are valid)
        """
        issues = {}
        
        for unicode_char, ascii_replacement in self._mappings.items():
            # Check if Unicode character is actually Unicode
            if len(unicode_char) == 1 and ord(unicode_char) < 128:
                issues[unicode_char] = f"Character '{unicode_char}' is already ASCII"
            
            # Check if replacement contains non-ASCII characters
            try:
                ascii_replacement.encode('ascii')
            except UnicodeEncodeError:
                issues[unicode_char] = f"Replacement '{ascii_replacement}' contains non-ASCII characters"
            
            # Check for empty replacements
            if not ascii_replacement.strip():
                issues[unicode_char] = "Replacement is empty or whitespace-only"
        
        if issues:
            self.logger.warning(f"Found {len(issues)} mapping validation issues")
        else:
            self.logger.debug("All character mappings validated successfully")
        
        return issues
    
    def get_statistics(self) -> Dict[str, any]:
        """
        Get statistics about the character mapper configuration.
        
        Returns:
            Dictionary with mapper statistics and configuration info
        """
        validation_issues = self.validate_mappings()
        
        return {
            'mode': self.mode.value,
            'total_mappings': len(self._mappings),
            'default_mappings': len(self.DEFAULT_MAPPINGS),
            'custom_mappings': len(self._mappings) - len(self.DEFAULT_MAPPINGS),
            'validation_issues': len(validation_issues),
            'has_validation_errors': len(validation_issues) > 0,
            'mapping_categories': {
                'emoji': sum(1 for char in self._mappings.keys() if len(char) == 1 and ord(char) > 0x1F000),
                'symbols': sum(1 for char in self._mappings.keys() if len(char) == 1 and 0x2000 <= ord(char) <= 0x2FFF),
                'currency': sum(1 for char in self._mappings.keys() if len(char) == 1 and 0x20A0 <= ord(char) <= 0x20CF),
                'box_drawing': sum(1 for char in self._mappings.keys() if len(char) == 1 and 0x2500 <= ord(char) <= 0x257F),
                'other': sum(1 for char in self._mappings.keys() if len(char) == 1 and (ord(char) < 0x2000 or ord(char) > 0x2FFF)),
                'multi_char': sum(1 for char in self._mappings.keys() if len(char) > 1)
            }
        }


class EncodingSafeFormatter(logging.Formatter):
    """
    A logging formatter that ensures safe encoding of log messages across different console environments.
    
    This formatter extends Python's standard logging.Formatter to provide automatic character mapping
    and encoding error handling, ensuring that log messages display correctly regardless of console
    encoding capabilities.
    """
    
    def __init__(self, 
                 fmt: Optional[str] = None,
                 datefmt: Optional[str] = None,
                 style: str = '%',
                 validate: bool = True,
                 character_mapper: Optional[CharacterMapper] = None,
                 encoding_detector: Optional[EncodingDetector] = None,
                 fallback_on_error: bool = True,
                 debug_encoding_issues: bool = False,
                 health_monitor: Optional['EncodingHealthMonitor'] = None):
        """
        Initialize the encoding-safe formatter.
        
        Args:
            fmt: Format string for log messages (same as logging.Formatter)
            datefmt: Date format string (same as logging.Formatter)
            style: Format style ('%', '{', or '$') (same as logging.Formatter)
            validate: Whether to validate the format string (same as logging.Formatter)
            character_mapper: CharacterMapper instance for Unicode to ASCII conversion
            encoding_detector: EncodingDetector instance for encoding detection
            fallback_on_error: Whether to fall back to ASCII mode on encoding errors
            debug_encoding_issues: Whether to log encoding issues for debugging
            health_monitor: EncodingHealthMonitor instance for health tracking
        """
        # Initialize parent formatter
        super().__init__(fmt=fmt, datefmt=datefmt, style=style, validate=validate)
        
        # Set up logging for this formatter
        self.logger = logging.getLogger('unicode_logging.formatter')
        
        # Initialize encoding detection and character mapping
        self.encoding_detector = encoding_detector or EncodingDetector(self.logger)
        self.fallback_on_error = fallback_on_error
        self.debug_encoding_issues = debug_encoding_issues
        
        # Initialize health monitoring
        self.health_monitor = health_monitor or EncodingHealthMonitor(self.logger)
        self.ascii_safe_logger = ASCIISafeDebugLogger(self.logger)
        
        # Initialize character mapper based on detected encoding
        if character_mapper:
            self.character_mapper = character_mapper
        else:
            # Auto-detect optimal logging mode
            recommended_mode = self.encoding_detector.get_recommended_mode()
            self.character_mapper = CharacterMapper(recommended_mode, logger=self.logger)
        
        # Enhanced error tracking and recovery
        self._encoding_error_count = 0
        self._max_encoding_errors = 3  # Switch to ASCII mode after this many errors
        self._has_switched_to_fallback = False
        self._fallback_levels = [LoggingMode.SAFE, LoggingMode.ASCII]
        self._current_fallback_level = 0
        self._last_encoding_check = 0
        self._encoding_check_interval = 60.0  # seconds
        self._recovery_attempt_count = 0
        self._max_recovery_attempts = 3
        
        # Cache for performance optimization
        self._message_cache = {}
        self._cache_max_size = 1000
        
        # Runtime monitoring
        self._runtime_monitoring_enabled = True
        self._last_health_check = time.time()
        self._health_check_interval = 30.0  # seconds
        
        self.ascii_safe_logger.debug(f"EncodingSafeFormatter initialized with mode: {self.character_mapper.mode.value}")
    
    def format(self, record: logging.LogRecord) -> str:
        """
        Format the specified record as text, applying encoding-safe transformations.
        
        This method overrides the parent format method to add Unicode-to-ASCII character
        mapping and encoding error handling while maintaining compatibility with the
        existing log record structure.
        
        Args:
            record: LogRecord instance to format
            
        Returns:
            Formatted log message string with encoding-safe characters
        """
        try:
            # Perform runtime health monitoring
            self._perform_runtime_monitoring()
            
            # First, format the message using the parent formatter
            formatted_message = super().format(record)
            
            # Apply encoding-safe transformations
            safe_message = self.safe_format_message(formatted_message)
            
            # Record successful operation
            self.health_monitor.record_operation(success=True)
            
            return safe_message
            
        except Exception as e:
            # Record failed operation
            error_type = type(e).__name__
            self.health_monitor.record_operation(
                success=False, 
                error_type=error_type, 
                error_details=str(e)
            )
            
            # Handle any formatting errors gracefully
            return self._handle_formatting_error(record, e)
    
    def safe_format_message(self, message: str) -> str:
        """
        Apply encoding-safe transformations to a log message.
        
        This method handles Unicode character mapping and encoding error recovery
        to ensure the message can be safely displayed on the current console.
        
        Args:
            message: Original log message string
            
        Returns:
            Encoding-safe version of the message
        """
        if not message:
            return message
        
        # Check cache first for performance
        if message in self._message_cache:
            return self._message_cache[message]
        
        try:
            start_time = time.time()
            
            # Apply character mapping based on current mode
            safe_message = self.character_mapper.map_unicode_to_safe(message)
            
            # Record character mapping metrics
            if hasattr(self.health_monitor, 'record_character_mapping'):
                mapping_time = time.time() - start_time
                unicode_chars = set(char for char in message if ord(char) > 127)
                chars_mapped = len([char for char in message if char in self.character_mapper._mappings])
                
                if chars_mapped > 0:
                    self.health_monitor.record_character_mapping(
                        characters_mapped=chars_mapped,
                        mapping_time=mapping_time,
                        unique_chars=unicode_chars
                    )
            
            # Record mode usage
            if hasattr(self.health_monitor, 'record_mode_usage'):
                self.health_monitor.record_mode_usage(self.character_mapper.mode)
            
            # Test if the message can be encoded with the console encoding
            safe_message = self._test_and_fix_encoding(safe_message)
            
            # Cache the result if cache isn't full
            if len(self._message_cache) < self._cache_max_size:
                self._message_cache[message] = safe_message
            
            return safe_message
            
        except Exception as e:
            # Handle encoding errors gracefully
            return self.handle_encoding_error(e, message)
    
    def _test_and_fix_encoding(self, message: str) -> str:
        """
        Test if a message can be encoded with the console encoding and fix if needed.
        
        Args:
            message: Message to test and potentially fix
            
        Returns:
            Message that can be safely encoded for console output
        """
        # Get console encoding info
        encoding_info = self.encoding_detector.detect_console_encoding()
        console_encoding = encoding_info.console_encoding
        
        try:
            # Test encoding with the console encoding
            message.encode(console_encoding)
            return message  # Message is safe as-is
            
        except UnicodeEncodeError as e:
            if self.debug_encoding_issues:
                self.logger.debug(f"Encoding error with {console_encoding}: {e}")
            
            # Try to fix specific problematic characters
            fixed_message = self._fix_encoding_issues(message, console_encoding, e)
            
            # Test the fixed message
            try:
                fixed_message.encode(console_encoding)
                return fixed_message
            except UnicodeEncodeError:
                # If still failing, fall back to more aggressive fixing
                return self._aggressive_encoding_fix(message, console_encoding)
    
    def _fix_encoding_issues(self, message: str, encoding: str, error: UnicodeEncodeError) -> str:
        """
        Fix specific encoding issues in a message.
        
        Args:
            message: Original message with encoding issues
            encoding: Target encoding
            error: UnicodeEncodeError that occurred
            
        Returns:
            Message with encoding issues fixed
        """
        fixed_message = message
        
        # Get the problematic character(s) from the error
        if hasattr(error, 'object') and hasattr(error, 'start') and hasattr(error, 'end'):
            problematic_chars = error.object[error.start:error.end]
            
            # Try to replace each problematic character
            for char in problematic_chars:
                replacement = self.character_mapper.get_replacement(char)
                if replacement != char:
                    fixed_message = fixed_message.replace(char, replacement)
                else:
                    # If no mapping exists, use a generic replacement
                    fixed_message = fixed_message.replace(char, '?')
                    
                    if self.debug_encoding_issues:
                        self.logger.debug(f"No mapping found for character '{char}' (U+{ord(char):04X}), using '?'")
        
        return fixed_message
    
    def _aggressive_encoding_fix(self, message: str, encoding: str) -> str:
        """
        Apply aggressive encoding fixes when normal fixes fail.
        
        Args:
            message: Message that still has encoding issues
            encoding: Target encoding
            
        Returns:
            Message with all encoding issues resolved
        """
        try:
            # Try encoding with error handling
            encoded = message.encode(encoding, errors='replace')
            return encoded.decode(encoding)
        except Exception:
            # Last resort: encode as ASCII with replacement
            try:
                encoded = message.encode('ascii', errors='replace')
                return encoded.decode('ascii')
            except Exception:
                # Ultimate fallback: return a safe error message
                return "[ENCODING ERROR: Message could not be displayed safely]"
    
    def handle_encoding_error(self, error: Exception, original_message: str) -> str:
        """
        Handle encoding errors gracefully with enhanced fallback mechanisms.
        
        Args:
            error: The encoding error that occurred
            original_message: The original message that caused the error
            
        Returns:
            A safe fallback message that can be displayed
        """
        self._encoding_error_count += 1
        error_type = type(error).__name__
        
        # Log the error using ASCII-safe logger
        self.ascii_safe_logger.log_encoding_error(error, "message formatting")
        
        # Record the error in health monitoring
        self.health_monitor.record_operation(
            success=False,
            error_type=error_type,
            error_details=str(error)
        )
        
        # Try progressive fallback if not already in fallback mode
        if (self.fallback_on_error and 
            self._encoding_error_count >= self._max_encoding_errors and 
            not self._has_switched_to_fallback):
            
            self._switch_to_progressive_fallback(error)
        
        # Try multiple recovery strategies
        recovery_strategies = [
            self._try_character_mapping_recovery,
            self._try_encoding_specific_recovery,
            self._try_aggressive_ascii_conversion,
            self._try_ultimate_fallback
        ]
        
        for strategy in recovery_strategies:
            try:
                result = strategy(original_message, error)
                if result:
                    return result
            except Exception as strategy_error:
                self.ascii_safe_logger.debug(f"Recovery strategy failed: {strategy_error}")
                continue
        
        # If all strategies fail, return ultimate fallback
        return f"[ENCODING ERROR: Message could not be safely displayed]"
    
    def _switch_to_progressive_fallback(self, error: Exception) -> None:
        """
        Switch to progressive fallback mode with enhanced error recovery.
        
        Args:
            error: The error that triggered the fallback
        """
        self._has_switched_to_fallback = True
        old_mode = self.character_mapper.mode
        
        # Use progressive fallback to determine new mode
        new_mode = self._progressive_fallback(error)
        self.character_mapper.update_mode(new_mode)
        
        # Clear the message cache since mode has changed
        self._message_cache.clear()
        
        # Log the mode switch using ASCII-safe logger
        self.ascii_safe_logger.log_mode_switch(
            old_mode.value,
            new_mode.value,
            f"encoding error threshold reached ({self._encoding_error_count} errors)"
        )
    
    def _try_character_mapping_recovery(self, message: str, error: Exception) -> Optional[str]:
        """
        Try to recover by applying more aggressive character mapping.
        
        Args:
            message: Original message
            error: The encoding error
            
        Returns:
            Recovered message or None if recovery failed
        """
        try:
            # Force ASCII mode temporarily for this message
            original_mode = self.character_mapper.mode
            self.character_mapper.mode = LoggingMode.ASCII
            
            # Apply character mapping
            mapped_message = self.character_mapper.map_unicode_to_safe(message)
            
            # Restore original mode
            self.character_mapper.mode = original_mode
            
            # Test if the mapped message can be encoded
            encoding_info = self.encoding_detector.detect_console_encoding()
            mapped_message.encode(encoding_info.console_encoding)
            
            return mapped_message
            
        except Exception:
            return None
    
    def _try_encoding_specific_recovery(self, message: str, error: Exception) -> Optional[str]:
        """
        Try encoding-specific recovery strategies.
        
        Args:
            message: Original message
            error: The encoding error
            
        Returns:
            Recovered message or None if recovery failed
        """
        try:
            encoding_info = self.encoding_detector.detect_console_encoding()
            console_encoding = encoding_info.console_encoding
            
            # Try encoding with different error handling strategies
            error_handlers = ['replace', 'ignore', 'xmlcharrefreplace']
            
            for handler in error_handlers:
                try:
                    encoded = message.encode(console_encoding, errors=handler)
                    decoded = encoded.decode(console_encoding)
                    return decoded
                except Exception:
                    continue
            
            return None
            
        except Exception:
            return None
    
    def _try_aggressive_ascii_conversion(self, message: str, error: Exception) -> Optional[str]:
        """
        Try aggressive ASCII conversion as a recovery strategy.
        
        Args:
            message: Original message
            error: The encoding error
            
        Returns:
            ASCII-converted message or None if conversion failed
        """
        try:
            # Convert all non-ASCII characters to ASCII equivalents or remove them
            ascii_chars = []
            for char in message:
                if ord(char) < 128:
                    ascii_chars.append(char)
                elif char in self.character_mapper._mappings:
                    ascii_chars.append(self.character_mapper._mappings[char])
                else:
                    # Replace with question mark or skip
                    ascii_chars.append('?')
            
            result = ''.join(ascii_chars)
            
            # Test if result can be encoded
            result.encode('ascii')
            return result
            
        except Exception:
            return None
    
    def _try_ultimate_fallback(self, message: str, error: Exception) -> Optional[str]:
        """
        Ultimate fallback strategy that should always succeed.
        
        Args:
            message: Original message
            error: The encoding error
            
        Returns:
            Safe fallback message
        """
        try:
            # Create a safe message with basic information
            safe_length = len(message) if message else 0
            error_type = type(error).__name__
            
            return f"[ENCODING ERROR: {error_type}, message length: {safe_length}]"
            
        except Exception:
            # Absolute last resort
            return "[CRITICAL ENCODING ERROR]"
    
    def _switch_to_fallback_mode(self) -> None:
        """
        Switch the formatter to fallback mode (ASCII-only) due to repeated encoding errors.
        """
        self._has_switched_to_fallback = True
        old_mode = self.character_mapper.mode
        self.character_mapper.update_mode(LoggingMode.ASCII)
        
        # Clear the message cache since mode has changed
        self._message_cache.clear()
        
        # Log the mode switch using ASCII-safe characters only
        fallback_message = f"[WARNING] Switched logging mode from {old_mode.value} to ASCII due to encoding errors"
        self.logger.warning(fallback_message)
    
    def _handle_formatting_error(self, record: logging.LogRecord, error: Exception) -> str:
        """
        Handle errors that occur during the formatting process.
        
        Args:
            record: The log record that caused the error
            error: The formatting error that occurred
            
        Returns:
            A safe error message that can be logged
        """
        if self.debug_encoding_issues:
            self.logger.debug(f"Formatting error: {error}")
        
        # Create a basic safe message with essential information
        try:
            safe_message = f"[FORMATTING ERROR] {record.levelname}: {getattr(record, 'msg', 'Unknown message')}"
            return self.safe_format_message(safe_message)
        except Exception:
            # Ultimate fallback for formatting errors
            return f"[CRITICAL FORMATTING ERROR] Level: {getattr(record, 'levelname', 'UNKNOWN')}"
    
    def update_character_mapper(self, character_mapper: CharacterMapper) -> None:
        """
        Update the character mapper used by this formatter.
        
        Args:
            character_mapper: New CharacterMapper instance to use
        """
        old_mode = self.character_mapper.mode if self.character_mapper else None
        self.character_mapper = character_mapper
        
        # Clear cache since mapping has changed
        self._message_cache.clear()
        
        new_mode = character_mapper.mode
        self.logger.debug(f"Character mapper updated from {old_mode} to {new_mode.value}")
    
    def update_logging_mode(self, mode: LoggingMode) -> None:
        """
        Update the logging mode for the character mapper.
        
        Args:
            mode: New logging mode to use
        """
        if self.character_mapper:
            old_mode = self.character_mapper.mode
            self.character_mapper.update_mode(mode)
            
            # Clear cache since mode has changed
            self._message_cache.clear()
            
            # Reset fallback state if switching away from ASCII
            if mode != LoggingMode.ASCII:
                self._has_switched_to_fallback = False
                self._encoding_error_count = 0
            
            self.logger.debug(f"Logging mode updated from {old_mode.value} to {mode.value}")
    
    def get_encoding_statistics(self) -> Dict[str, any]:
        """
        Get statistics about encoding operations performed by this formatter.
        
        Returns:
            Dictionary with encoding statistics and current configuration
        """
        encoding_info = self.encoding_detector.detect_console_encoding()
        
        return {
            'current_mode': self.character_mapper.mode.value if self.character_mapper else 'unknown',
            'encoding_error_count': self._encoding_error_count,
            'has_switched_to_fallback': self._has_switched_to_fallback,
            'fallback_on_error': self.fallback_on_error,
            'debug_encoding_issues': self.debug_encoding_issues,
            'message_cache_size': len(self._message_cache),
            'message_cache_max_size': self._cache_max_size,
            'console_encoding': encoding_info.console_encoding,
            'supports_unicode': encoding_info.supports_unicode,
            'detection_confidence': encoding_info.detection_confidence,
            'character_mapper_stats': self.character_mapper.get_statistics() if self.character_mapper else {}
        }
    
    def clear_cache(self) -> None:
        """
        Clear the internal message cache.
        
        This can be useful when console settings change or when memory usage needs to be reduced.
        """
        cache_size = len(self._message_cache)
        self._message_cache.clear()
        self.logger.debug(f"Cleared message cache ({cache_size} entries)")
    
    def reset_error_tracking(self) -> None:
        """
        Reset encoding error tracking and fallback state.
        
        This allows the formatter to retry Unicode mode after errors have been resolved.
        """
        self._encoding_error_count = 0
        self._has_switched_to_fallback = False
        self.logger.debug("Reset encoding error tracking and fallback state")
    
    def test_message_formatting(self, test_message: str) -> Dict[str, any]:
        """
        Test message formatting and return detailed results for debugging.
        
        Args:
            test_message: Message to test formatting on
            
        Returns:
            Dictionary with test results and transformation details
        """
        results = {
            'original_message': test_message,
            'original_length': len(test_message),
            'contains_unicode': any(ord(char) > 127 for char in test_message),
            'unicode_chars': [char for char in test_message if ord(char) > 127],
        }
        
        try:
            # Test character mapping
            mapped_message = self.character_mapper.map_unicode_to_safe(test_message)
            results['mapped_message'] = mapped_message
            results['mapping_applied'] = mapped_message != test_message
            results['mapped_length'] = len(mapped_message)
            
            # Test encoding safety
            encoding_info = self.encoding_detector.detect_console_encoding()
            console_encoding = encoding_info.console_encoding
            
            try:
                mapped_message.encode(console_encoding)
                results['encoding_safe'] = True
                results['encoding_error'] = None
            except UnicodeEncodeError as e:
                results['encoding_safe'] = False
                results['encoding_error'] = str(e)
            
            # Test full formatting
            safe_message = self.safe_format_message(test_message)
            results['final_message'] = safe_message
            results['final_length'] = len(safe_message)
            results['transformations_applied'] = safe_message != test_message
            
            results['success'] = True
            
        except Exception as e:
            results['success'] = False
            results['error'] = str(e)
        
        return results
    
    def get_health_status(self) -> Dict[str, any]:
        """
        Get comprehensive health status for the encoding-safe formatter.
        
        Returns:
            Dictionary with health status and diagnostic information
        """
        base_stats = self.get_encoding_statistics()
        health_status = self.health_monitor.get_health_status()
        
        return {
            'formatter_stats': base_stats,
            'health_monitoring': health_status,
            'recovery_info': {
                'has_switched_to_fallback': self._has_switched_to_fallback,
                'current_fallback_level': self._current_fallback_level,
                'recovery_attempt_count': self._recovery_attempt_count,
                'max_recovery_attempts': self._max_recovery_attempts,
                'runtime_monitoring_enabled': self._runtime_monitoring_enabled
            },
            'diagnostic_info': self.health_monitor.get_diagnostic_info()
        }
    
    def enable_runtime_monitoring(self) -> None:
        """Enable runtime monitoring of encoding health."""
        self._runtime_monitoring_enabled = True
        self.ascii_safe_logger.info("Runtime encoding monitoring enabled")
    
    def disable_runtime_monitoring(self) -> None:
        """Disable runtime monitoring of encoding health."""
        self._runtime_monitoring_enabled = False
        self.ascii_safe_logger.info("Runtime encoding monitoring disabled")
    
    def force_health_check(self) -> Dict[str, any]:
        """
        Force an immediate health check and return results.
        
        Returns:
            Dictionary with health check results and any actions taken
        """
        try:
            self._last_health_check = 0  # Force immediate check
            self._perform_runtime_monitoring()
            
            health_status = self.health_monitor.get_health_status()
            recovery_recommendation = self.health_monitor.get_recovery_recommendation()
            
            return {
                'health_status': health_status,
                'recovery_recommendation': recovery_recommendation,
                'current_mode': self.character_mapper.mode.value,
                'fallback_active': self._has_switched_to_fallback,
                'check_timestamp': time.time()
            }
            
        except Exception as e:
            self.ascii_safe_logger.error(f"Error during forced health check: {e}")
            return {
                'error': str(e),
                'check_timestamp': time.time()
            }
    
    def reset_error_recovery(self) -> None:
        """
        Reset error recovery state and health monitoring.
        
        This can be useful after resolving encoding issues or changing system configuration.
        """
        # Reset formatter state
        self._encoding_error_count = 0
        self._has_switched_to_fallback = False
        self._current_fallback_level = 0
        self._recovery_attempt_count = 0
        self._message_cache.clear()
        
        # Reset health monitoring
        self.health_monitor.reset_health_tracking()
        
        # Re-detect encoding and update mode
        try:
            self.encoding_detector.invalidate_cache()
            encoding_info = self.encoding_detector.detect_console_encoding()
            recommended_mode = encoding_info.recommended_mode
            
            old_mode = self.character_mapper.mode
            self.character_mapper.update_mode(recommended_mode)
            
            self.ascii_safe_logger.log_mode_switch(
                old_mode.value,
                recommended_mode.value,
                "error recovery reset"
            )
            
        except Exception as e:
            self.ascii_safe_logger.error(f"Error during recovery reset: {e}")
        
        self.ascii_safe_logger.info("Error recovery state reset completed")
    
    def _perform_runtime_monitoring(self) -> None:
        """
        Perform runtime monitoring of encoding health and automatic recovery.
        
        This method checks encoding health, detects console encoding changes,
        and automatically switches modes when necessary.
        """
        if not self._runtime_monitoring_enabled:
            return
        
        current_time = time.time()
        
        # Skip if too recent
        if current_time - self._last_health_check < self._health_check_interval:
            return
        
        self._last_health_check = current_time
        
        try:
            # Check for console encoding changes
            encoding_changed = self.health_monitor.check_encoding_changes(self.encoding_detector)
            
            if encoding_changed:
                self.ascii_safe_logger.info("Console encoding change detected, re-evaluating logging mode")
                self._handle_encoding_change()
            
            # Check if we should switch to fallback mode
            if self.health_monitor.should_switch_to_fallback():
                self._handle_health_based_fallback()
            
            # Check if we can recover from fallback mode
            elif self._has_switched_to_fallback and self._can_attempt_recovery():
                self._attempt_recovery()
                
        except Exception as e:
            self.ascii_safe_logger.error(f"Error during runtime monitoring: {e}")
    
    def _handle_encoding_change(self) -> None:
        """
        Handle console encoding changes by re-detecting optimal mode.
        """
        try:
            # Re-detect encoding and get new recommendation
            self.encoding_detector.invalidate_cache()
            encoding_info = self.encoding_detector.detect_console_encoding()
            new_mode = encoding_info.recommended_mode
            
            # Update character mapper if mode should change
            if new_mode != self.character_mapper.mode:
                old_mode = self.character_mapper.mode
                self.character_mapper.update_mode(new_mode)
                self._message_cache.clear()
                
                self.ascii_safe_logger.log_mode_switch(
                    old_mode.value, 
                    new_mode.value, 
                    "console encoding change"
                )
                
                # Reset fallback state since we have new encoding info
                self._has_switched_to_fallback = False
                self._current_fallback_level = 0
                self._recovery_attempt_count = 0
                
        except Exception as e:
            self.ascii_safe_logger.error(f"Error handling encoding change: {e}")
    
    def _handle_health_based_fallback(self) -> None:
        """
        Handle fallback mode switching based on health monitoring.
        """
        if self._has_switched_to_fallback:
            return  # Already in fallback mode
        
        try:
            # Get recovery recommendation
            recommendation = self.health_monitor.get_recovery_recommendation()
            recommended_action = recommendation.get('recommended_action')
            recommended_mode = recommendation.get('recommended_mode')
            reason = recommendation.get('reason', 'health monitoring')
            
            if recommended_action in ['switch_to_ascii', 'switch_to_safe', 'temporary_ascii']:
                # Switch to recommended fallback mode
                if recommended_mode == 'ascii':
                    new_mode = LoggingMode.ASCII
                    self._current_fallback_level = 1
                elif recommended_mode == 'safe':
                    new_mode = LoggingMode.SAFE
                    self._current_fallback_level = 0
                else:
                    new_mode = LoggingMode.ASCII
                    self._current_fallback_level = 1
                
                old_mode = self.character_mapper.mode
                self.character_mapper.update_mode(new_mode)
                self._has_switched_to_fallback = True
                self._message_cache.clear()
                
                self.ascii_safe_logger.log_mode_switch(
                    old_mode.value,
                    new_mode.value,
                    reason
                )
                
        except Exception as e:
            self.ascii_safe_logger.error(f"Error handling health-based fallback: {e}")
    
    def _can_attempt_recovery(self) -> bool:
        """
        Check if we can attempt recovery from fallback mode.
        
        Returns:
            True if recovery should be attempted, False otherwise
        """
        # Don't attempt recovery if we've tried too many times
        if self._recovery_attempt_count >= self._max_recovery_attempts:
            return False
        
        # Check if enough time has passed since last error
        health_status = self.health_monitor.get_health_status()
        time_since_last_error = health_status.get('time_since_last_error')
        
        if time_since_last_error is None or time_since_last_error < 300.0:  # 5 minutes
            return False
        
        # Check if error rate is low enough
        recent_error_rate = health_status.get('recent_error_rate_5min', 100.0)
        if recent_error_rate > 5.0:  # 5% error rate threshold
            return False
        
        return True
    
    def _attempt_recovery(self) -> None:
        """
        Attempt to recover from fallback mode to a higher-capability mode.
        """
        try:
            self._recovery_attempt_count += 1
            
            # Re-detect encoding capabilities
            self.encoding_detector.invalidate_cache()
            encoding_info = self.encoding_detector.detect_console_encoding()
            recommended_mode = encoding_info.recommended_mode
            
            # Only attempt recovery if recommended mode is better than current
            current_mode = self.character_mapper.mode
            
            if (current_mode == LoggingMode.ASCII and 
                recommended_mode in [LoggingMode.UNICODE, LoggingMode.SAFE]):
                
                old_mode = current_mode
                self.character_mapper.update_mode(recommended_mode)
                self._message_cache.clear()
                
                # Test the new mode with a sample message
                test_message = "Test recovery: ✅ Unicode test"
                try:
                    safe_test = self.safe_format_message(test_message)
                    
                    # If test succeeds, recovery is successful
                    self._has_switched_to_fallback = False
                    self._current_fallback_level = 0
                    self._recovery_attempt_count = 0
                    
                    self.ascii_safe_logger.log_mode_switch(
                        old_mode.value,
                        recommended_mode.value,
                        "automatic recovery"
                    )
                    
                except Exception as test_error:
                    # Recovery failed, revert to fallback mode
                    self.character_mapper.update_mode(old_mode)
                    self.ascii_safe_logger.error(f"Recovery attempt failed: {test_error}")
                    
        except Exception as e:
            self.ascii_safe_logger.error(f"Error during recovery attempt: {e}")
    
    def _progressive_fallback(self, original_error: Exception) -> LoggingMode:
        """
        Implement progressive fallback through different encoding modes.
        
        Args:
            original_error: The original error that triggered fallback
            
        Returns:
            New logging mode to try
        """
        if self._current_fallback_level < len(self._fallback_levels):
            new_mode = self._fallback_levels[self._current_fallback_level]
            self._current_fallback_level += 1
            
            self.ascii_safe_logger.log_mode_switch(
                self.character_mapper.mode.value,
                new_mode.value,
                f"progressive fallback due to {type(original_error).__name__}"
            )
            
            return new_mode
        else:
            # Already at the most restrictive fallback level
            return LoggingMode.ASCII


class EncodingHealthMonitor:
    """
    Monitors encoding health and provides runtime error detection and recovery recommendations.
    
    This class tracks encoding errors over time, monitors console encoding changes,
    and provides health status information for the Unicode-safe logging system.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None, window_size: int = 100):
        """
        Initialize the encoding health monitor.
        
        Args:
            logger: Optional logger instance for debug output
            window_size: Size of the sliding window for error rate calculation
        """
        self.logger = logger or logging.getLogger('unicode_logging.health_monitor')
        self.window_size = window_size
        
        # Error tracking
        self._error_history = deque(maxlen=window_size)
        self._error_types = defaultdict(int)
        self._total_operations = 0
        self._total_errors = 0
        
        # Timing tracking
        self._last_error_time = None
        self._error_burst_threshold = 5  # errors per second to trigger burst detection
        self._error_burst_window = 1.0  # seconds
        
        # Console encoding tracking
        self._last_encoding_check = None
        self._encoding_history = []
        self._encoding_change_count = 0
        
        # Health status
        self._health_status = "healthy"
        self._last_health_check = time.time()
        self._health_check_interval = 30.0  # seconds
        
        # Encoding mode usage metrics
        self._mode_usage_stats = defaultdict(int)
        self._mode_switch_history = []
        self._mode_switch_count = 0
        self._current_mode = None
        self._mode_start_time = time.time()
        self._mode_duration_stats = defaultdict(float)
        
        # Performance metrics
        self._character_mapping_stats = {
            'total_mappings_applied': 0,
            'unique_characters_mapped': set(),
            'mapping_performance_samples': deque(maxlen=100)
        }
        
        # System integration metrics
        self._system_health_reports = []
        self._execution_summary_entries = []
        self._error_report_entries = []
        
        # Thread safety
        self._lock = threading.Lock()
        
        self.logger.debug("EncodingHealthMonitor initialized with enhanced metrics collection")
    
    def record_operation(self, success: bool, error_type: Optional[str] = None, 
                        error_details: Optional[str] = None) -> None:
        """
        Record an encoding operation result.
        
        Args:
            success: Whether the operation was successful
            error_type: Type of error if operation failed
            error_details: Additional error details
        """
        with self._lock:
            current_time = time.time()
            self._total_operations += 1
            
            if success:
                self._error_history.append((current_time, False, None, None))
            else:
                self._total_errors += 1
                self._last_error_time = current_time
                self._error_history.append((current_time, True, error_type, error_details))
                
                if error_type:
                    self._error_types[error_type] += 1
                
                # Check for error bursts
                self._check_error_burst(current_time)
    
    def _check_error_burst(self, current_time: float) -> None:
        """
        Check if we're experiencing an error burst.
        
        Args:
            current_time: Current timestamp
        """
        # Count errors in the last burst window
        burst_start = current_time - self._error_burst_window
        recent_errors = sum(1 for timestamp, is_error, _, _ in self._error_history 
                           if is_error and timestamp >= burst_start)
        
        if recent_errors >= self._error_burst_threshold:
            self._health_status = "error_burst"
            self.logger.warning(f"[ASCII-SAFE] Encoding error burst detected: {recent_errors} errors in {self._error_burst_window}s")
    
    def check_encoding_changes(self, encoding_detector: 'EncodingDetector') -> bool:
        """
        Check if console encoding has changed since last check.
        
        Args:
            encoding_detector: EncodingDetector instance to use for checking
            
        Returns:
            True if encoding has changed, False otherwise
        """
        with self._lock:
            current_time = time.time()
            
            # Skip check if too recent
            if (self._last_encoding_check and 
                current_time - self._last_encoding_check < self._health_check_interval):
                return False
            
            try:
                # Force fresh detection by invalidating cache
                encoding_detector.invalidate_cache()
                current_encoding_info = encoding_detector.detect_console_encoding()
                current_encoding = current_encoding_info.console_encoding
                
                # Check if encoding changed
                if self._encoding_history:
                    last_encoding = self._encoding_history[-1][1]
                    if current_encoding != last_encoding:
                        self._encoding_change_count += 1
                        self.logger.info(f"[ASCII-SAFE] Console encoding changed: {last_encoding} -> {current_encoding}")
                        self._encoding_history.append((current_time, current_encoding))
                        self._last_encoding_check = current_time
                        return True
                else:
                    # First check
                    self._encoding_history.append((current_time, current_encoding))
                
                self._last_encoding_check = current_time
                return False
                
            except Exception as e:
                self.logger.debug(f"[ASCII-SAFE] Error checking encoding changes: {e}")
                return False
    
    def get_error_rate(self, time_window: Optional[float] = None) -> float:
        """
        Calculate the current error rate.
        
        Args:
            time_window: Time window in seconds (None for all recorded operations)
            
        Returns:
            Error rate as a percentage (0.0 to 100.0)
        """
        with self._lock:
            if not self._error_history:
                return 0.0
            
            if time_window is None:
                # Calculate overall error rate
                if self._total_operations == 0:
                    return 0.0
                return (self._total_errors / self._total_operations) * 100.0
            
            # Calculate error rate for specific time window
            current_time = time.time()
            window_start = current_time - time_window
            
            window_operations = [entry for entry in self._error_history 
                               if entry[0] >= window_start]
            
            if not window_operations:
                return 0.0
            
            error_count = sum(1 for _, is_error, _, _ in window_operations if is_error)
            return (error_count / len(window_operations)) * 100.0
    
    def get_health_status(self) -> Dict[str, any]:
        """
        Get comprehensive health status information.
        
        Returns:
            Dictionary with health status and statistics
        """
        with self._lock:
            current_time = time.time()
            
            # Update health status based on current conditions
            self._update_health_status(current_time)
            
            return {
                'status': self._health_status,
                'total_operations': self._total_operations,
                'total_errors': self._total_errors,
                'overall_error_rate': self.get_error_rate(),
                'recent_error_rate_1min': self.get_error_rate(60.0),
                'recent_error_rate_5min': self.get_error_rate(300.0),
                'error_types': dict(self._error_types),
                'encoding_changes': self._encoding_change_count,
                'last_error_time': self._last_error_time,
                'time_since_last_error': current_time - self._last_error_time if self._last_error_time else None,
                'monitoring_window_size': self.window_size,
                'history_length': len(self._error_history),
                'current_encoding': self._encoding_history[-1][1] if self._encoding_history else None,
                'last_health_check': self._last_health_check
            }
    
    def _update_health_status(self, current_time: float) -> None:
        """
        Update the health status based on current conditions.
        
        Args:
            current_time: Current timestamp
        """
        # Check if we need to update health status
        if current_time - self._last_health_check < self._health_check_interval:
            return
        
        self._last_health_check = current_time
        
        # Calculate recent error rates
        error_rate_1min = self.get_error_rate(60.0)
        error_rate_5min = self.get_error_rate(300.0)
        
        # Determine health status
        if error_rate_1min > 50.0:
            self._health_status = "critical"
        elif error_rate_1min > 20.0:
            self._health_status = "degraded"
        elif error_rate_5min > 10.0:
            self._health_status = "warning"
        elif self._last_error_time and (current_time - self._last_error_time) > 300.0:
            self._health_status = "healthy"
        elif not self._last_error_time:
            self._health_status = "healthy"
        else:
            self._health_status = "stable"
    
    def should_switch_to_fallback(self) -> bool:
        """
        Determine if the system should switch to fallback mode based on health status.
        
        Returns:
            True if fallback mode is recommended, False otherwise
        """
        with self._lock:
            current_time = time.time()
            self._update_health_status(current_time)
            
            # Switch to fallback if health is critical or degraded
            if self._health_status in ["critical", "degraded", "error_burst"]:
                return True
            
            # Switch if error rate is consistently high
            if self.get_error_rate(60.0) > 30.0 and self.get_error_rate(300.0) > 15.0:
                return True
            
            return False
    
    def get_recovery_recommendation(self) -> Dict[str, any]:
        """
        Get recommendations for error recovery based on current health status.
        
        Returns:
            Dictionary with recovery recommendations
        """
        with self._lock:
            current_time = time.time()
            self._update_health_status(current_time)
            
            recommendations = {
                'recommended_action': 'none',
                'recommended_mode': None,
                'reason': '',
                'urgency': 'low'
            }
            
            if self._health_status == "critical":
                recommendations.update({
                    'recommended_action': 'switch_to_ascii',
                    'recommended_mode': 'ascii',
                    'reason': 'Critical error rate detected',
                    'urgency': 'high'
                })
            elif self._health_status == "degraded":
                recommendations.update({
                    'recommended_action': 'switch_to_safe',
                    'recommended_mode': 'safe',
                    'reason': 'High error rate detected',
                    'urgency': 'medium'
                })
            elif self._health_status == "error_burst":
                recommendations.update({
                    'recommended_action': 'temporary_ascii',
                    'recommended_mode': 'ascii',
                    'reason': 'Error burst detected',
                    'urgency': 'high'
                })
            elif self._encoding_change_count > 3:
                recommendations.update({
                    'recommended_action': 'redetect_encoding',
                    'recommended_mode': 'auto',
                    'reason': 'Multiple encoding changes detected',
                    'urgency': 'medium'
                })
            
            return recommendations
    
    def reset_health_tracking(self) -> None:
        """
        Reset health tracking statistics.
        
        This can be useful after resolving encoding issues or changing system configuration.
        """
        with self._lock:
            self._error_history.clear()
            self._error_types.clear()
            self._total_operations = 0
            self._total_errors = 0
            self._last_error_time = None
            self._health_status = "healthy"
            self._last_health_check = time.time()
            
            self.logger.debug("[ASCII-SAFE] Health tracking statistics reset")
    
    def record_mode_usage(self, mode: 'LoggingMode', duration: Optional[float] = None) -> None:
        """
        Record encoding mode usage for metrics collection.
        
        Args:
            mode: The logging mode being used
            duration: Optional duration the mode was active (in seconds)
        """
        with self._lock:
            current_time = time.time()
            
            # Update mode usage statistics
            self._mode_usage_stats[mode.value] += 1
            
            # Track mode switches
            if self._current_mode != mode:
                if self._current_mode is not None:
                    # Calculate duration of previous mode
                    mode_duration = current_time - self._mode_start_time
                    self._mode_duration_stats[self._current_mode.value] += mode_duration
                    
                    # Record mode switch
                    self._mode_switch_history.append({
                        'timestamp': current_time,
                        'from_mode': self._current_mode.value,
                        'to_mode': mode.value,
                        'duration': mode_duration
                    })
                    self._mode_switch_count += 1
                
                self._current_mode = mode
                self._mode_start_time = current_time
            
            # Add explicit duration if provided
            if duration is not None:
                self._mode_duration_stats[mode.value] += duration
    
    def record_character_mapping(self, characters_mapped: int, mapping_time: float = 0.0, 
                               unique_chars: Optional[set] = None) -> None:
        """
        Record character mapping performance metrics.
        
        Args:
            characters_mapped: Number of characters that were mapped
            mapping_time: Time taken for mapping operation (in seconds)
            unique_chars: Set of unique characters that were mapped
        """
        with self._lock:
            self._character_mapping_stats['total_mappings_applied'] += characters_mapped
            
            if unique_chars:
                self._character_mapping_stats['unique_characters_mapped'].update(unique_chars)
            
            if mapping_time > 0:
                self._character_mapping_stats['mapping_performance_samples'].append({
                    'timestamp': time.time(),
                    'characters_mapped': characters_mapped,
                    'mapping_time': mapping_time,
                    'chars_per_second': characters_mapped / mapping_time if mapping_time > 0 else 0
                })
    
    def get_encoding_mode_metrics(self) -> Dict[str, any]:
        """
        Get comprehensive encoding mode usage metrics.
        
        Returns:
            Dictionary with mode usage statistics and performance data
        """
        with self._lock:
            current_time = time.time()
            
            # Calculate current mode duration
            current_mode_duration = 0.0
            if self._current_mode is not None:
                current_mode_duration = current_time - self._mode_start_time
            
            # Calculate total runtime
            total_runtime = sum(self._mode_duration_stats.values()) + current_mode_duration
            
            # Calculate mode usage percentages
            mode_percentages = {}
            if total_runtime > 0:
                for mode, duration in self._mode_duration_stats.items():
                    mode_percentages[mode] = (duration / total_runtime) * 100
                
                if self._current_mode and current_mode_duration > 0:
                    current_mode_key = self._current_mode.value
                    current_pct = (current_mode_duration / total_runtime) * 100
                    mode_percentages[current_mode_key] = mode_percentages.get(current_mode_key, 0) + current_pct
            
            # Get recent mode switches
            recent_switches = [switch for switch in self._mode_switch_history 
                             if current_time - switch['timestamp'] < 3600.0]  # Last hour
            
            # Calculate character mapping performance
            mapping_performance = {}
            if self._character_mapping_stats['mapping_performance_samples']:
                samples = list(self._character_mapping_stats['mapping_performance_samples'])
                mapping_performance = {
                    'average_chars_per_second': sum(s['chars_per_second'] for s in samples) / len(samples),
                    'total_samples': len(samples),
                    'recent_samples': len([s for s in samples if current_time - s['timestamp'] < 300.0])
                }
            
            return {
                'mode_usage_counts': dict(self._mode_usage_stats),
                'mode_duration_stats': dict(self._mode_duration_stats),
                'mode_usage_percentages': mode_percentages,
                'current_mode': self._current_mode.value if self._current_mode else None,
                'current_mode_duration': current_mode_duration,
                'total_runtime': total_runtime,
                'mode_switch_count': self._mode_switch_count,
                'recent_mode_switches': recent_switches[-10:],  # Last 10 switches
                'character_mapping_stats': {
                    'total_mappings_applied': self._character_mapping_stats['total_mappings_applied'],
                    'unique_characters_count': len(self._character_mapping_stats['unique_characters_mapped']),
                    'performance': mapping_performance
                }
            }
    
    def add_system_health_report_entry(self, entry: Dict[str, any]) -> None:
        """
        Add an entry to the system health report for encoding status.
        
        Args:
            entry: Dictionary with health report information
        """
        with self._lock:
            entry['timestamp'] = time.time()
            self._system_health_reports.append(entry)
            
            # Keep only recent entries (last 24 hours)
            cutoff_time = time.time() - 86400.0  # 24 hours
            self._system_health_reports = [
                report for report in self._system_health_reports 
                if report['timestamp'] > cutoff_time
            ]
    
    def add_execution_summary_entry(self, entry: Dict[str, any]) -> None:
        """
        Add an entry to the execution summary for encoding information.
        
        Args:
            entry: Dictionary with execution summary information
        """
        with self._lock:
            entry['timestamp'] = time.time()
            self._execution_summary_entries.append(entry)
            
            # Keep only recent entries (last 7 days)
            cutoff_time = time.time() - 604800.0  # 7 days
            self._execution_summary_entries = [
                entry for entry in self._execution_summary_entries 
                if entry['timestamp'] > cutoff_time
            ]
    
    def add_error_report_entry(self, entry: Dict[str, any]) -> None:
        """
        Add an entry to error reports with encoding context.
        
        Args:
            entry: Dictionary with error report information
        """
        with self._lock:
            entry['timestamp'] = time.time()
            self._error_report_entries.append(entry)
            
            # Keep only recent entries (last 24 hours)
            cutoff_time = time.time() - 86400.0  # 24 hours
            self._error_report_entries = [
                entry for entry in self._error_report_entries 
                if entry['timestamp'] > cutoff_time
            ]
    
    def get_system_health_report_data(self) -> Dict[str, any]:
        """
        Get encoding-related data for system health reports.
        
        Returns:
            Dictionary with health report data
        """
        with self._lock:
            current_time = time.time()
            health_status = self.get_health_status()
            mode_metrics = self.get_encoding_mode_metrics()
            
            # Calculate health score (0-100)
            health_score = 100
            error_rate = health_status.get('overall_error_rate', 0)
            if error_rate > 0:
                health_score = max(0, 100 - (error_rate * 2))  # Reduce score by 2 points per % error rate
            
            # Determine health level
            if health_score >= 90:
                health_level = "excellent"
            elif health_score >= 75:
                health_level = "good"
            elif health_score >= 50:
                health_level = "fair"
            elif health_score >= 25:
                health_level = "poor"
            else:
                health_level = "critical"
            
            return {
                'encoding_health': {
                    'status': health_status.get('status', 'unknown'),
                    'health_score': health_score,
                    'health_level': health_level,
                    'error_rate': error_rate,
                    'total_operations': health_status.get('total_operations', 0),
                    'total_errors': health_status.get('total_errors', 0),
                    'last_error_time': health_status.get('last_error_time'),
                    'encoding_changes': health_status.get('encoding_changes', 0)
                },
                'mode_usage': {
                    'current_mode': mode_metrics.get('current_mode'),
                    'mode_switches': mode_metrics.get('mode_switch_count', 0),
                    'mode_percentages': mode_metrics.get('mode_usage_percentages', {}),
                    'total_runtime': mode_metrics.get('total_runtime', 0)
                },
                'performance': {
                    'character_mappings': mode_metrics.get('character_mapping_stats', {}),
                    'recent_errors': len([entry for entry in self._error_report_entries 
                                        if current_time - entry['timestamp'] < 3600.0])  # Last hour
                },
                'recommendations': self.get_recovery_recommendation()
            }
    
    def get_execution_summary_data(self) -> Dict[str, any]:
        """
        Get encoding-related data for execution summaries.
        
        Returns:
            Dictionary with execution summary data
        """
        with self._lock:
            mode_metrics = self.get_encoding_mode_metrics()
            health_status = self.get_health_status()
            
            # Calculate summary statistics
            total_operations = health_status.get('total_operations', 0)
            total_errors = health_status.get('total_errors', 0)
            success_rate = ((total_operations - total_errors) / total_operations * 100) if total_operations > 0 else 100
            
            return {
                'encoding_summary': {
                    'total_operations': total_operations,
                    'total_errors': total_errors,
                    'success_rate': success_rate,
                    'error_rate': health_status.get('overall_error_rate', 0),
                    'final_status': health_status.get('status', 'unknown')
                },
                'mode_usage_summary': {
                    'modes_used': list(mode_metrics.get('mode_usage_counts', {}).keys()),
                    'mode_switches': mode_metrics.get('mode_switch_count', 0),
                    'primary_mode': max(mode_metrics.get('mode_usage_percentages', {}).items(), 
                                      key=lambda x: x[1], default=('unknown', 0))[0],
                    'total_runtime': mode_metrics.get('total_runtime', 0)
                },
                'character_mapping_summary': {
                    'total_mappings': mode_metrics.get('character_mapping_stats', {}).get('total_mappings_applied', 0),
                    'unique_characters': mode_metrics.get('character_mapping_stats', {}).get('unique_characters_count', 0)
                },
                'console_info': {
                    'encoding_changes': health_status.get('encoding_changes', 0),
                    'current_encoding': self._encoding_history[-1][1] if self._encoding_history else 'unknown'
                }
            }
    
    def get_error_report_data(self, error_context: Optional[Dict[str, any]] = None) -> Dict[str, any]:
        """
        Get encoding-related data for error reports.
        
        Args:
            error_context: Optional context about the error being reported
            
        Returns:
            Dictionary with error report data including encoding context
        """
        with self._lock:
            current_time = time.time()
            health_status = self.get_health_status()
            mode_metrics = self.get_encoding_mode_metrics()
            
            # Get recent encoding errors
            recent_encoding_errors = [
                entry for entry in self._error_report_entries 
                if current_time - entry['timestamp'] < 300.0  # Last 5 minutes
            ]
            
            # Get recent mode switches that might be related to errors
            recent_mode_switches = [
                switch for switch in self._mode_switch_history 
                if current_time - switch['timestamp'] < 300.0  # Last 5 minutes
            ]
            
            error_report_data = {
                'encoding_context': {
                    'current_mode': mode_metrics.get('current_mode'),
                    'current_encoding': self._encoding_history[-1][1] if self._encoding_history else 'unknown',
                    'health_status': health_status.get('status', 'unknown'),
                    'error_rate': health_status.get('overall_error_rate', 0),
                    'recent_errors_count': len(recent_encoding_errors),
                    'recent_mode_switches': len(recent_mode_switches)
                },
                'error_history': {
                    'total_encoding_errors': health_status.get('total_errors', 0),
                    'error_types': dict(self._error_types),
                    'recent_encoding_errors': recent_encoding_errors[-5:],  # Last 5 errors
                    'last_error_time': health_status.get('last_error_time')
                },
                'system_state': {
                    'mode_switches_today': len([
                        switch for switch in self._mode_switch_history 
                        if current_time - switch['timestamp'] < 86400.0  # Last 24 hours
                    ]),
                    'encoding_changes_today': len([
                        change for change in self._encoding_history 
                        if current_time - change[0] < 86400.0  # Last 24 hours
                    ]),
                    'total_operations': health_status.get('total_operations', 0)
                },
                'recommendations': self.get_recovery_recommendation()
            }
            
            # Add specific error context if provided
            if error_context:
                error_report_data['specific_error_context'] = error_context
            
            return error_report_data
    
    def get_diagnostic_info(self) -> Dict[str, any]:
        """
        Get detailed diagnostic information for troubleshooting.
        
        Returns:
            Dictionary with comprehensive diagnostic data
        """
        with self._lock:
            current_time = time.time()
            
            # Get recent error details
            recent_errors = [(timestamp, error_type, error_details) 
                           for timestamp, is_error, error_type, error_details in self._error_history 
                           if is_error and current_time - timestamp < 300.0]
            
            base_diagnostic = {
                'health_status': self.get_health_status(),
                'recovery_recommendation': self.get_recovery_recommendation(),
                'recent_errors': recent_errors[-10:],  # Last 10 errors
                'error_type_distribution': dict(self._error_types),
                'encoding_history': self._encoding_history[-5:],  # Last 5 encoding changes
                'monitoring_config': {
                    'window_size': self.window_size,
                    'error_burst_threshold': self._error_burst_threshold,
                    'error_burst_window': self._error_burst_window,
                    'health_check_interval': self._health_check_interval
                },
                'system_info': {
                    'platform': sys.platform,
                    'python_version': sys.version,
                    'current_time': current_time
                }
            }
            
            # Add enhanced monitoring data
            base_diagnostic.update({
                'mode_metrics': self.get_encoding_mode_metrics(),
                'system_health_data': self.get_system_health_report_data(),
                'execution_summary_data': self.get_execution_summary_data(),
                'error_report_data': self.get_error_report_data(),
                'monitoring_statistics': {
                    'health_reports_count': len(self._system_health_reports),
                    'execution_entries_count': len(self._execution_summary_entries),
                    'error_entries_count': len(self._error_report_entries)
                }
            })
            
            return base_diagnostic


class ASCIISafeDebugLogger:
    """
    A debug logger that ensures all messages use only ASCII characters.
    
    This logger is specifically designed for logging encoding-related issues
    without causing circular encoding problems.
    """
    
    def __init__(self, base_logger: logging.Logger, prefix: str = "[ASCII-SAFE]"):
        """
        Initialize the ASCII-safe debug logger.
        
        Args:
            base_logger: Base logger to use for output
            prefix: Prefix to add to all messages
        """
        self.base_logger = base_logger
        self.prefix = prefix
        self._message_count = 0
    
    def _ensure_ascii(self, message: str) -> str:
        """
        Ensure a message contains only ASCII characters.
        
        Args:
            message: Original message
            
        Returns:
            ASCII-safe version of the message
        """
        try:
            # Try to encode as ASCII
            message.encode('ascii')
            return message
        except UnicodeEncodeError:
            # Replace non-ASCII characters with safe alternatives
            safe_chars = []
            for char in message:
                if ord(char) < 128:
                    safe_chars.append(char)
                else:
                    # Replace with Unicode code point notation
                    safe_chars.append(f'U+{ord(char):04X}')
            return ''.join(safe_chars)
    
    def debug(self, message: str, *args, **kwargs) -> None:
        """Log a debug message with ASCII-safe characters."""
        safe_message = self._ensure_ascii(f"{self.prefix} {message}")
        self._message_count += 1
        self.base_logger.debug(safe_message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs) -> None:
        """Log an info message with ASCII-safe characters."""
        safe_message = self._ensure_ascii(f"{self.prefix} {message}")
        self._message_count += 1
        self.base_logger.info(safe_message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs) -> None:
        """Log a warning message with ASCII-safe characters."""
        safe_message = self._ensure_ascii(f"{self.prefix} {message}")
        self._message_count += 1
        self.base_logger.warning(safe_message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs) -> None:
        """Log an error message with ASCII-safe characters."""
        safe_message = self._ensure_ascii(f"{self.prefix} {message}")
        self._message_count += 1
        self.base_logger.error(safe_message, *args, **kwargs)
    
    def log_encoding_error(self, error: Exception, context: str = "") -> None:
        """
        Log an encoding error with safe ASCII characters.
        
        Args:
            error: The encoding error that occurred
            context: Additional context about where the error occurred
        """
        error_type = type(error).__name__
        error_message = str(error)
        safe_error_message = self._ensure_ascii(error_message)
        
        if context:
            safe_context = self._ensure_ascii(context)
            self.error(f"Encoding error in {safe_context}: {error_type} - {safe_error_message}")
        else:
            self.error(f"Encoding error: {error_type} - {safe_error_message}")
    
    def log_mode_switch(self, old_mode: str, new_mode: str, reason: str) -> None:
        """
        Log a logging mode switch with safe ASCII characters.
        
        Args:
            old_mode: Previous logging mode
            new_mode: New logging mode
            reason: Reason for the switch
        """
        safe_old_mode = self._ensure_ascii(old_mode)
        safe_new_mode = self._ensure_ascii(new_mode)
        safe_reason = self._ensure_ascii(reason)
        
        self.warning(f"Logging mode switched: {safe_old_mode} -> {safe_new_mode} ({safe_reason})")
    
    def log_health_status(self, health_status: Dict[str, any]) -> None:
        """
        Log encoding health status with safe ASCII characters.
        
        Args:
            health_status: Health status dictionary
        """
        status = health_status.get('status', 'unknown')
        error_rate = health_status.get('overall_error_rate', 0.0)
        total_errors = health_status.get('total_errors', 0)
        
        self.info(f"Encoding health: {status} (Error rate: {error_rate:.1f}%, Total errors: {total_errors})")
    
    def get_message_count(self) -> int:
        """
        Get the number of messages logged by this ASCII-safe logger.
        
        Returns:
            Number of messages logged
        """
        return self._message_count