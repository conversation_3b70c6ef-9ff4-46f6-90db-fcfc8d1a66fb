# Project Structure

## Directory Organization

```
rl-portfolio-rebalancing/
├── rl_portfolio_rebalancing.py    # Main system implementation
├── config.py                      # Configuration settings
├── requirements.txt               # Python dependencies
├── install_dependencies.py       # Installation script
├── README.md                      # Project documentation
├── data/                          # Market data storage
├── models/                        # Trained model storage
├── logs/                          # System logs
├── results/                       # Performance results
├── config/                        # Additional configuration files
└── __pycache__/                   # Python bytecode cache
```

## Core Files

### Main Implementation
- **`rl_portfolio_rebalancing.py`**: Primary system entry point containing setup, logging, and dependency verification
- **`config.py`**: Centralized configuration with all system parameters organized by category

### Dependencies & Setup
- **`requirements.txt`**: Standard Python package dependencies
- **`install_dependencies.py`**: Automated installation script for all dependencies
- **`README.md`**: Comprehensive project documentation with usage instructions

## Data Organization

### Runtime Directories
- **`data/`**: Historical market data, ETF prices, and preprocessed datasets
- **`models/`**: Trained PPO models and checkpoints
- **`logs/`**: Timestamped log files with format `rl_portfolio_rebalancing_YYYYMMDD_HHMMSS.log`
- **`results/`**: Performance metrics, backtesting results, and evaluation outputs
- **`config/`**: Additional configuration files and parameter sets

## Code Architecture

### Modular Design Principles
- **Component-based**: Follows TensorTrade's modular architecture
- **Configuration-driven**: All parameters externalized to `config.py`
- **Logging-first**: Comprehensive logging throughout the system
- **Dependency verification**: Built-in checks for all required packages

### Key Design Patterns
- **Dataclass schemas**: `MarketData`, `PortfolioState`, `PerformanceMetrics`
- **Factory pattern**: Environment creation through TensorTrade's `create()` function
- **Strategy pattern**: Configurable ActionScheme and RewardScheme implementations
- **Observer pattern**: TensorTrade's component system with registered names

## File Naming Conventions

### Log Files
- Format: `rl_portfolio_rebalancing_YYYYMMDD_HHMMSS.log`
- Location: `logs/` directory
- Automatic timestamping on system startup

### Model Files
- Trained models saved in `models/` directory
- Include metadata: training date, performance metrics, hyperparameters

### Configuration Files
- Main config: `config.py`
- Additional configs in `config/` directory
- Environment-specific settings supported