#!/usr/bin/env python3
"""
Installation script for RL Portfolio Rebalancing System dependencies
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✓ {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {command}")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Main installation function."""
    print("="*60)
    print("RL Portfolio Rebalancing System - Dependency Installation")
    print("="*60)
    
    # Check if pip is available
    if not run_command("pip --version"):
        print("Error: pip is not available. Please install pip first.")
        sys.exit(1)
    
    # Install standard packages from requirements.txt
    print("\n1. Installing standard packages...")
    if os.path.exists("requirements.txt"):
        if not run_command("pip install -r requirements.txt"):
            print("Warning: Some standard packages failed to install.")
    else:
        print("Warning: requirements.txt not found. Installing packages individually...")
        packages = [
            "pandas>=1.5.0",
            "numpy>=1.21.0", 
            "yfinance>=0.2.0",
            "ta>=0.10.0",
            "stable-baselines3>=2.0.0",
            "gym>=0.21.0",
            "matplotlib>=3.5.0",
            "seaborn>=0.11.0",
            "scikit-learn>=1.1.0",
            "scipy>=1.9.0"
        ]
        
        for package in packages:
            run_command(f"pip install {package}")
    
    # Install TensorTrade from GitHub
    print("\n2. Installing TensorTrade from GitHub...")
    if not run_command("pip install git+https://github.com/tensortrade-org/tensortrade.git"):
        print("Warning: TensorTrade installation failed. You may need to install it manually.")
        print("Try: pip install git+https://github.com/tensortrade-org/tensortrade.git")
    
    print("\n" + "="*60)
    print("Installation completed!")
    print("="*60)
    print("\nTo verify the installation, run:")
    print("python rl_portfolio_rebalancing.py")
    print("\nIf you encounter any issues, please check the error messages above.")

if __name__ == "__main__":
    main()