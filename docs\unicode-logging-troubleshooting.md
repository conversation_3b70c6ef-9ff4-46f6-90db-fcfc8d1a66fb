# Unicode Logging Troubleshooting Guide

## Quick Diagnosis

### Step 1: Check Your Console Encoding

**Windows Command Prompt:**
```cmd
chcp
```
Common outputs:
- `Active code page: 65001` = UTF-8 (Unicode supported)
- `Active code page: 950` = Traditional Chinese (Unicode issues likely)
- `Active code page: 1252` = Western European (Limited Unicode)

**PowerShell:**
```powershell
[Console]::OutputEncoding
[Console]::InputEncoding
```

### Step 2: Test Unicode Support

Create a test file `test_unicode.py`:
```python
import sys

print(f"Python version: {sys.version}")
print(f"Default encoding: {sys.getdefaultencoding()}")
print(f"Console encoding: {sys.stdout.encoding}")
print(f"File system encoding: {sys.getfilesystemencoding()}")

# Test Unicode characters
test_chars = ['✅', '❌', '⚠️', '🎯', '🚀']
for char in test_chars:
    try:
        print(f"Testing {char}: ", end='')
        print(char)
    except UnicodeEncodeError as e:
        print(f"FAILED - {e}")
```

Run with: `python test_unicode.py`

## Common Problems and Solutions

### Problem 1: Characters Display as Question Marks (?)

**Symptoms:**
```
? Portfolio optimization completed
? Warning: High volatility detected
```

**Cause:** Console doesn't support Unicode characters

**Solutions:**

1. **Automatic Fix (Recommended):**
   ```python
   UNICODE_LOGGING = {
       'mode': 'auto'  # Let system detect best mode
   }
   ```

2. **Manual Fix:**
   ```python
   UNICODE_LOGGING = {
       'mode': 'ascii'  # Force ASCII mode
   }
   ```

3. **Console Fix (Windows):**
   ```cmd
   chcp 65001  # Switch to UTF-8
   ```

### Problem 2: UnicodeEncodeError Exceptions

**Symptoms:**
```
UnicodeEncodeError: 'cp950' codec can't encode character '\u2705' in position 0
```

**Cause:** Console encoding doesn't support specific Unicode characters

**Solutions:**

1. **Enable Fallback (Recommended):**
   ```python
   UNICODE_LOGGING = {
       'fallback_on_error': True  # Auto-switch to ASCII on errors
   }
   ```

2. **Debug the Issue:**
   ```python
   UNICODE_LOGGING = {
       'debug_encoding_issues': True  # See detailed error info
   }
   ```

3. **Force Safe Mode:**
   ```python
   UNICODE_LOGGING = {
       'mode': 'ascii'  # Prevent Unicode usage entirely
   }
   ```

### Problem 3: Mixed Character Display

**Symptoms:**
```
[OK] Portfolio rebalanced ✅  # Some work, some don't
[FAIL] Error occurred ❌
```

**Cause:** Partial Unicode support or inconsistent character mapping

**Solutions:**

1. **Consistent Mode:**
   ```python
   UNICODE_LOGGING = {
       'mode': 'ascii'  # Use ASCII for all characters
   }
   ```

2. **Custom Mappings:**
   ```python
   UNICODE_LOGGING = {
       'custom_mappings': {
           '✅': '[SUCCESS]',
           '❌': '[ERROR]',
           '⚠️': '[WARN]'
       }
   }
   ```

### Problem 4: Performance Issues

**Symptoms:**
- Slow logging performance
- High CPU usage during logging

**Solutions:**

1. **Disable Debug Mode:**
   ```python
   UNICODE_LOGGING = {
       'debug_encoding_issues': False  # Reduce overhead
   }
   ```

2. **Optimize Character Mappings:**
   ```python
   # Only map characters you actually use
   UNICODE_LOGGING = {
       'custom_mappings': {
           '✅': '[OK]',  # Only essential mappings
           '❌': '[FAIL]'
       }
   }
   ```

### Problem 5: Configuration Not Applied

**Symptoms:**
- Settings in config.py ignored
- Default behavior despite configuration

**Solutions:**

1. **Check Configuration Loading:**
   ```python
   # Verify config is loaded
   from config import UNICODE_LOGGING
   print(f"Unicode config: {UNICODE_LOGGING}")
   ```

2. **Environment Variable Override:**
   ```bash
   # Check for env var overrides
   echo %UNICODE_LOGGING_MODE%
   ```

3. **Explicit Initialization:**
   ```python
   from unicode_logging import setup_unicode_logging, UnicodeLoggingConfig
   
   config = UnicodeLoggingConfig(mode='ascii')
   setup_unicode_logging(config)
   ```

## Environment-Specific Issues

### Windows with Chinese Locale (CP950)

**Problem:** Traditional Chinese Windows systems use CP950 encoding which doesn't support many Unicode characters.

**Solution:**
```python
UNICODE_LOGGING = {
    'mode': 'auto',  # Will detect CP950 and use ASCII
    'fallback_on_error': True,
    'custom_mappings': {
        # Use Chinese-friendly ASCII alternatives
        '✅': '[成功]',  # If Chinese characters work
        '❌': '[失敗]',
        '⚠️': '[警告]'
    }
}
```

### Windows PowerShell vs Command Prompt

**Problem:** Different behavior between PowerShell and Command Prompt.

**PowerShell Solution:**
```powershell
# Set UTF-8 encoding
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
```

**Command Prompt Solution:**
```cmd
chcp 65001
```

### Windows Terminal vs Legacy Console

**Problem:** Windows Terminal supports Unicode better than legacy console.

**Detection:**
```python
import os
is_windows_terminal = os.environ.get('WT_SESSION') is not None
```

**Solution:**
```python
UNICODE_LOGGING = {
    'mode': 'unicode' if is_windows_terminal else 'auto'
}
```

## Advanced Troubleshooting

### Enable Comprehensive Debugging

Create a debug configuration:
```python
DEBUG_UNICODE_LOGGING = {
    'mode': 'auto',
    'fallback_on_error': True,
    'debug_encoding_issues': True,
    'custom_mappings': {}
}
```

This will output detailed information:
```
DEBUG: Starting encoding detection...
DEBUG: Console encoding: cp950
DEBUG: Testing Unicode character: ✅
DEBUG: UnicodeEncodeError caught: 'cp950' codec can't encode character
DEBUG: Unicode support: False
DEBUG: Recommended mode: ascii
DEBUG: Character mapper initialized with mode: ascii
DEBUG: Mapping applied: ✅ -> [OK]
```

### Manual Encoding Detection

Test encoding detection manually:
```python
from unicode_logging import EncodingDetector

detector = EncodingDetector()
info = detector.detect_console_encoding()

print(f"Console encoding: {info.console_encoding}")
print(f"Supports UTF-8: {info.supports_utf8}")
print(f"Supports Unicode: {info.supports_unicode}")
print(f"Recommended mode: {info.recommended_mode}")
print(f"Confidence: {info.detection_confidence}")
```

### Custom Character Testing

Test specific characters:
```python
from unicode_logging import CharacterMapper

mapper = CharacterMapper(mode='auto')
test_chars = ['✅', '❌', '⚠️', '🎯', '🚀', '💻']

for char in test_chars:
    replacement = mapper.get_replacement(char)
    print(f"{char} -> {replacement}")
```

### Performance Profiling

Profile logging performance:
```python
import time
import logging
from unicode_logging import setup_unicode_logging

setup_unicode_logging()
logger = logging.getLogger(__name__)

# Test performance
start_time = time.time()
for i in range(1000):
    logger.info(f"✅ Test message {i}")
end_time = time.time()

print(f"1000 log messages took: {end_time - start_time:.3f} seconds")
```

## Recovery Procedures

### Complete Reset

If Unicode logging is completely broken:

1. **Disable Unicode Logging:**
   ```python
   UNICODE_LOGGING = {
       'mode': 'ascii',
       'fallback_on_error': False,
       'debug_encoding_issues': False
   }
   ```

2. **Clear Python Cache:**
   ```bash
   # Remove cached bytecode
   rmdir /s __pycache__
   ```

3. **Restart Application:**
   ```bash
   python main.py
   ```

### Fallback to Standard Logging

If Unicode logging fails completely:
```python
import logging

# Bypass Unicode logging entirely
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)
logger.info("Standard logging active")
```

## Getting Help

### Diagnostic Information to Collect

When reporting issues, include:

1. **System Information:**
   ```python
   import sys, os, platform
   print(f"OS: {platform.system()} {platform.release()}")
   print(f"Python: {sys.version}")
   print(f"Console encoding: {sys.stdout.encoding}")
   print(f"Environment: {os.environ.get('TERM', 'Unknown')}")
   ```

2. **Configuration:**
   ```python
   from config import UNICODE_LOGGING
   print(f"Config: {UNICODE_LOGGING}")
   ```

3. **Error Messages:**
   - Full stack traces
   - Debug output (if enabled)
   - Console output examples

### Common Support Questions

**Q: Why do I see [OK] instead of ✅?**
A: Your console doesn't support Unicode. This is normal and expected behavior.

**Q: Can I force Unicode mode even if detection says no?**
A: Yes, set `mode: 'unicode'`, but expect encoding errors.

**Q: How do I add my own character mappings?**
A: Use the `custom_mappings` configuration option.

**Q: Does this affect logging performance?**
A: Minimal impact (~1-2ms per message). Disable debug mode for best performance.

**Q: Can I use this with other logging frameworks?**
A: Yes, it works with any Python logging that uses standard formatters.