# RL Portfolio Rebalancing System Dependencies

# Core data manipulation and numerical computing
pandas>=1.5.0
numpy>=1.21.0

# Financial data fetching
yfinance>=0.2.0

# Technical analysis
ta>=0.10.0

# TensorTrade framework (install from GitHub)
# pip install git+https://github.com/tensortrade-org/tensortrade.git

# Reinforcement Learning
stable-baselines3>=2.0.0
gym>=0.21.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Additional utilities
scikit-learn>=1.1.0
scipy>=1.9.0