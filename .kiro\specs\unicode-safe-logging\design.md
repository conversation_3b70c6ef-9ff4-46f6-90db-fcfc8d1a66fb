# Design Document

## Overview

The Unicode-safe logging system will enhance the existing RL Portfolio Rebalancing System's logging infrastructure to handle Unicode characters gracefully across different Windows encoding environments. The solution implements automatic encoding detection, character mapping, and fallback mechanisms to ensure reliable logging output regardless of console encoding capabilities.

The design follows a layered approach with minimal disruption to existing code, using a custom logging formatter that intercepts log messages and applies encoding-safe transformations before output.

## Architecture

### Core Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  (Existing logging calls remain unchanged)                 │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                 EncodingSafeFormatter                      │
│  • Intercepts log messages                                 │
│  • Applies character transformations                       │
│  • Handles encoding detection results                      │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                 CharacterMapper                            │
│  • Maps Unicode to ASCII equivalents                       │
│  • Maintains visual hierarchy                              │
│  • Provides context-aware replacements                     │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                 EncodingDetector                           │
│  • Detects console encoding capabilities                   │
│  • Tests Unicode support                                   │
│  • Provides encoding recommendations                       │
└─────────────────────────────────────────────────────────────┘
```

### Integration Points

The system integrates with the existing `ComprehensiveLogger` class by:
1. Replacing the current logging formatter with `EncodingSafeFormatter`
2. Adding encoding detection during logger initialization
3. Providing configuration options through the existing config system

## Components and Interfaces

### EncodingDetector

```python
class EncodingDetector:
    def detect_console_encoding(self) -> EncodingInfo
    def test_unicode_support(self) -> bool
    def get_recommended_mode(self) -> LoggingMode
    def is_utf8_capable(self) -> bool
```

**Responsibilities:**
- Detect the current console's encoding capabilities
- Test whether Unicode characters can be safely output
- Provide recommendations for logging mode selection
- Cache detection results for performance

### CharacterMapper

```python
class CharacterMapper:
    def __init__(self, mode: LoggingMode)
    def map_unicode_to_safe(self, text: str) -> str
    def get_replacement(self, unicode_char: str) -> str
    def update_mode(self, mode: LoggingMode) -> None
```

**Character Mapping Table:**
- ✅ → [OK] or [SUCCESS]
- ❌ → [FAIL] or [ERROR]  
- 🎯 → [MILESTONE]
- 💻 → [RESOURCE]
- 🚀 → [PHASE]
- ⚠️ → [WARNING]
- 📊 → [DATA]
- 📈 → [METRICS]
- 🏥 → [HEALTH]
- 🔢 → [COUNT]

### EncodingSafeFormatter

```python
class EncodingSafeFormatter(logging.Formatter):
    def __init__(self, character_mapper: CharacterMapper)
    def format(self, record: LogRecord) -> str
    def safe_format_message(self, message: str) -> str
    def handle_encoding_error(self, error: UnicodeError, message: str) -> str
```

**Responsibilities:**
- Intercept all log messages before output
- Apply character mapping transformations
- Handle encoding errors gracefully
- Maintain original log formatting structure

### Configuration Integration

```python
@dataclass
class LoggingConfig:
    unicode_mode: str = "auto"  # "auto", "unicode", "ascii"
    fallback_on_error: bool = True
    debug_encoding_issues: bool = False
    character_mapping: Dict[str, str] = field(default_factory=dict)
```

## Data Models

### EncodingInfo

```python
@dataclass
class EncodingInfo:
    console_encoding: str
    supports_utf8: bool
    supports_unicode: bool
    recommended_mode: LoggingMode
    detection_confidence: float
    fallback_encoding: str
```

### LoggingMode

```python
class LoggingMode(Enum):
    AUTO = "auto"
    UNICODE = "unicode"
    ASCII = "ascii"
    SAFE = "safe"  # ASCII with enhanced visual markers
```

### CharacterMapping

```python
@dataclass
class CharacterMapping:
    unicode_char: str
    ascii_replacement: str
    context_sensitive: bool = False
    visual_weight: int = 1  # For maintaining hierarchy
```

## Error Handling

### Encoding Detection Failures
- **Fallback Strategy**: Default to ASCII mode if detection fails
- **Logging**: Log detection failures at DEBUG level to avoid circular issues
- **Recovery**: Retry detection on next logger initialization

### Runtime Encoding Errors
- **Graceful Degradation**: Automatically switch to ASCII mode on first encoding error
- **Error Logging**: Log encoding errors with safe ASCII characters only
- **User Notification**: Provide clear feedback about mode switching

### Configuration Errors
- **Validation**: Validate configuration options at startup
- **Defaults**: Use safe defaults for invalid configurations
- **Warnings**: Warn users about configuration issues using ASCII-safe messages

## Testing Strategy

### Unit Tests
1. **EncodingDetector Tests**
   - Mock different console encodings (cp950, utf-8, ascii)
   - Test Unicode support detection accuracy
   - Verify caching behavior

2. **CharacterMapper Tests**
   - Test all Unicode to ASCII mappings
   - Verify visual hierarchy preservation
   - Test mode switching functionality

3. **EncodingSafeFormatter Tests**
   - Test message transformation accuracy
   - Verify error handling behavior
   - Test integration with existing log formats

### Integration Tests
1. **Cross-Platform Testing**
   - Test on Windows with cp950 encoding
   - Test on Windows with UTF-8 encoding
   - Test on systems with limited Unicode support

2. **Performance Testing**
   - Measure logging performance impact
   - Test with high-volume log output
   - Verify memory usage patterns

3. **Compatibility Testing**
   - Test with existing ComprehensiveLogger functionality
   - Verify all existing log statements work unchanged
   - Test configuration integration

### System Tests
1. **End-to-End Scenarios**
   - Run full RL portfolio system with Unicode-safe logging
   - Test automatic mode detection and switching
   - Verify user experience improvements

2. **Error Recovery Testing**
   - Simulate encoding failures during runtime
   - Test fallback mechanism reliability
   - Verify system continues operation after encoding errors

## Implementation Considerations

### Performance Optimization
- Cache encoding detection results
- Minimize string processing overhead
- Use efficient character replacement algorithms
- Lazy initialization of character mappings

### Backward Compatibility
- Maintain existing logging API unchanged
- Preserve all current log formatting
- Ensure existing configuration options continue working
- Provide migration path for custom formatters

### Configuration Management
- Integrate with existing config.py structure
- Support environment variable overrides
- Provide runtime configuration updates
- Document configuration options clearly

### Monitoring and Diagnostics
- Add metrics for encoding mode usage
- Track encoding error frequency
- Provide diagnostic information for troubleshooting
- Include encoding info in system health checks