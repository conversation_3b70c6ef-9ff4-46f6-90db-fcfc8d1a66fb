# System Requirements

## Overview

This document outlines the system requirements for the RL Portfolio Rebalancing System, including the Unicode-safe logging feature that ensures reliable operation across different Windows encoding environments.

## Core System Requirements

### Python Environment
- **Python Version**: 3.8 or higher
- **Virtual Environment**: Recommended for dependency isolation
- **Package Manager**: pip (included with Python)

### Operating System Support
- **Primary**: Windows 10/11 (all locale configurations)
- **Secondary**: Linux, macOS (for development)
- **Console Support**: Command Prompt, PowerShell, Windows Terminal

### Hardware Requirements
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: 2GB free space for data and models
- **CPU**: Multi-core processor recommended for training

## Unicode Logging Requirements

### Console Encoding Support

The Unicode-safe logging system supports all Windows console encoding configurations:

#### Fully Supported Encodings
- **UTF-8 (CP65001)**: Full Unicode character support
- **Windows-1252 (CP1252)**: Western European, limited Unicode
- **ASCII (CP20127)**: Basic ASCII characters only

#### Problematic Encodings (Auto-Fallback)
- **Traditional Chinese (CP950)**: Limited Unicode support
- **Simplified Chinese (CP936)**: Limited Unicode support  
- **Japanese (CP932)**: Limited Unicode support
- **Korean (CP949)**: Limited Unicode support

#### Detection and Fallback
The system automatically:
1. Detects console encoding at startup
2. Tests Unicode character support
3. Switches to ASCII fallback when needed
4. Provides clear visual alternatives

### Configuration Requirements

#### Minimum Configuration
```python
# In config.py - minimal setup
UNICODE_LOGGING = {
    'mode': 'auto'  # Automatic detection and fallback
}
```

#### Recommended Configuration
```python
# In config.py - production setup
UNICODE_LOGGING = {
    'mode': 'auto',
    'fallback_on_error': True,
    'debug_encoding_issues': False,
    'custom_mappings': {}
}
```

#### Development Configuration
```python
# In config.py - development setup
UNICODE_LOGGING = {
    'mode': 'auto',
    'fallback_on_error': True,
    'debug_encoding_issues': True,  # Enable for troubleshooting
    'custom_mappings': {
        # Add project-specific mappings
    }
}
```

## Python Dependencies

### Core Dependencies
```
pandas>=1.5.0          # Data manipulation and analysis
numpy>=1.21.0          # Numerical computing
yfinance>=0.2.0        # Financial data fetching
ta>=0.10.0             # Technical analysis indicators
stable-baselines3>=2.0.0  # PPO algorithm implementation
gym>=0.21.0            # RL environment interface
matplotlib>=3.5.0      # Basic plotting
seaborn>=0.11.0        # Statistical visualization
scikit-learn>=1.1.0    # ML utilities
scipy>=1.9.0           # Scientific computing
```

### Unicode Logging Dependencies
The Unicode-safe logging system uses only Python standard library modules:
- `logging` - Core logging functionality
- `sys` - System-specific parameters and functions
- `os` - Operating system interface
- `platform` - Platform identification
- `dataclasses` - Data class support (Python 3.7+)
- `enum` - Enumeration support
- `typing` - Type hints

**No additional dependencies required** for Unicode logging functionality.

### Installation Methods

#### Automatic Installation (Recommended)
```bash
python install_dependencies.py
```

#### Manual Installation
```bash
# Install standard packages
pip install -r requirements.txt

# Install TensorTrade from GitHub (required)
pip install git+https://github.com/tensortrade-org/tensortrade.git
```

## Environment Setup

### Windows Console Configuration

#### Command Prompt Setup
```cmd
# Check current encoding
chcp

# Set UTF-8 encoding (optional, system will auto-detect)
chcp 65001
```

#### PowerShell Setup
```powershell
# Check current encoding
[Console]::OutputEncoding

# Set UTF-8 encoding (optional)
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
```

#### Windows Terminal (Recommended)
Windows Terminal provides the best Unicode support and is automatically detected by the system.

### Environment Variables

#### Unicode Logging Override
```bash
# Force specific logging mode
set UNICODE_LOGGING_MODE=ascii

# Enable debug output
set UNICODE_LOGGING_DEBUG=true

# Disable fallback behavior
set UNICODE_LOGGING_FALLBACK=false
```

#### System Configuration
```bash
# Python encoding (usually automatic)
set PYTHONIOENCODING=utf-8

# Locale settings (Windows)
set LANG=en_US.UTF-8
```

## File System Requirements

### Directory Structure
```
rl-portfolio-rebalancing/
├── config.py                     # System configuration
├── unicode_logging.py            # Unicode logging implementation
├── main.py                       # Main system entry point
├── data/                         # Market data storage
├── models/                       # Trained model storage
├── logs/                         # System logs (Unicode-safe)
├── results/                      # Performance results
├── docs/                         # Documentation
│   ├── unicode-safe-logging.md   # Unicode logging guide
│   ├── unicode-logging-troubleshooting.md
│   └── system-requirements.md    # This document
└── examples/                     # Configuration examples
    └── unicode_logging_examples.py
```

### File Permissions
- **Read/Write**: All directories must be writable for logging and data storage
- **Execute**: Python files must be executable
- **Unicode Support**: File system must support Unicode filenames

## Performance Requirements

### Unicode Logging Performance
- **Overhead**: <2ms per log message
- **Memory Usage**: ~50KB for character mapping tables
- **CPU Impact**: Negligible (<1% additional CPU usage)
- **Startup Time**: <100ms additional initialization time

### System Performance
- **Training Time**: 10-30 minutes per model (depending on hardware)
- **Memory Usage**: 2-4GB during training
- **Disk I/O**: Moderate (data loading and model saving)

## Compatibility Matrix

### Python Versions
| Python Version | Core System | Unicode Logging | Status |
|---------------|-------------|-----------------|---------|
| 3.8.x | ✅ Supported | ✅ Supported | Recommended |
| 3.9.x | ✅ Supported | ✅ Supported | Recommended |
| 3.10.x | ✅ Supported | ✅ Supported | Recommended |
| 3.11.x | ✅ Supported | ✅ Supported | Latest |
| 3.12.x | ⚠️ Testing | ✅ Supported | Beta |

### Windows Versions
| Windows Version | Console Support | Unicode Logging | Status |
|----------------|-----------------|-----------------|---------|
| Windows 10 | ✅ Full | ✅ Auto-detect | Supported |
| Windows 11 | ✅ Full | ✅ Auto-detect | Recommended |
| Windows Server 2019 | ✅ Full | ✅ Auto-detect | Supported |
| Windows Server 2022 | ✅ Full | ✅ Auto-detect | Supported |

### Console Applications
| Console | Unicode Support | Auto-Detection | Recommendation |
|---------|----------------|----------------|----------------|
| Windows Terminal | ✅ Excellent | ✅ Yes | **Recommended** |
| PowerShell 7+ | ✅ Good | ✅ Yes | Good |
| PowerShell 5.1 | ⚠️ Limited | ✅ Yes | Acceptable |
| Command Prompt | ⚠️ Limited | ✅ Yes | Basic |
| VS Code Terminal | ✅ Good | ✅ Yes | Good |
| PyCharm Terminal | ✅ Good | ✅ Yes | Good |

## Troubleshooting Requirements

### Diagnostic Tools
The system includes built-in diagnostic capabilities:

#### Encoding Detection Test
```python
from unicode_logging import EncodingDetector

detector = EncodingDetector()
info = detector.detect_console_encoding()
print(f"Console encoding: {info.console_encoding}")
print(f"Unicode support: {info.supports_unicode}")
```

#### Character Mapping Test
```python
from unicode_logging import CharacterMapper

mapper = CharacterMapper(mode='auto')
test_chars = ['✅', '❌', '⚠️', '🎯']
for char in test_chars:
    print(f"{char} -> {mapper.get_replacement(char)}")
```

### Log File Requirements
- **Location**: `logs/` directory (auto-created)
- **Format**: `rl_portfolio_rebalancing_YYYYMMDD_HHMMSS.log`
- **Encoding**: UTF-8 with BOM for Windows compatibility
- **Rotation**: Manual cleanup recommended

## Security Requirements

### File System Security
- **Log Files**: May contain sensitive trading data
- **Model Files**: Contain trained algorithms
- **Configuration**: May contain API keys or credentials

### Network Security
- **Data Sources**: HTTPS connections to financial data providers
- **Firewall**: Outbound HTTPS (port 443) required for data fetching
- **Proxy Support**: Standard Python requests proxy configuration

## Deployment Requirements

### Development Environment
```bash
# Clone repository
git clone <repository-url>
cd rl-portfolio-rebalancing

# Install dependencies
python install_dependencies.py

# Configure Unicode logging
# Edit config.py to set UNICODE_LOGGING options

# Run system
python main.py
```

### Production Environment
```bash
# Set production environment variables
set UNICODE_LOGGING_MODE=auto
set UNICODE_LOGGING_DEBUG=false

# Run with logging
python main.py > output.log 2>&1
```

### CI/CD Environment
```bash
# Force ASCII mode for CI reliability
set UNICODE_LOGGING_MODE=ascii
set UNICODE_LOGGING_FALLBACK=false

# Run tests
python -m pytest tests/
```

## Validation Checklist

### Pre-Installation
- [ ] Python 3.8+ installed
- [ ] pip package manager available
- [ ] 2GB+ free disk space
- [ ] Internet connection for data fetching

### Post-Installation
- [ ] All dependencies installed successfully
- [ ] Unicode logging auto-detection working
- [ ] Log files created in `logs/` directory
- [ ] Console output displays correctly
- [ ] No encoding errors in console

### System Validation
- [ ] Market data fetching successful
- [ ] Model training completes without errors
- [ ] Performance metrics generated
- [ ] Log files contain expected Unicode/ASCII characters
- [ ] System runs on target Windows configuration

## Support and Maintenance

### Regular Maintenance
- **Log Cleanup**: Periodically clean old log files
- **Dependency Updates**: Update packages as needed
- **Configuration Review**: Review Unicode logging settings

### Monitoring
- **Encoding Errors**: Monitor logs for encoding issues
- **Performance**: Track logging performance impact
- **Compatibility**: Test on new Windows updates

### Updates
- **Unicode Mappings**: Add new character mappings as needed
- **Encoding Support**: Enhance detection for new console types
- **Performance**: Optimize character mapping algorithms

For additional support, refer to:
- [Unicode-Safe Logging Documentation](unicode-safe-logging.md)
- [Troubleshooting Guide](unicode-logging-troubleshooting.md)
- [Configuration Examples](../examples/unicode_logging_examples.py)