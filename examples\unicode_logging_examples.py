#!/usr/bin/env python3
"""
Unicode Logging Configuration Examples

This file demonstrates various ways to configure the Unicode-safe logging system
for different use cases and environments.
"""

import logging
from unicode_logging import setup_unicode_logging, UnicodeLoggingConfig, CharacterMapper

# Example 1: Basic Auto-Detection (Recommended)
def example_basic_auto():
    """Most common configuration - let the system detect the best mode."""
    
    config = UnicodeLoggingConfig(
        mode='auto',
        fallback_on_error=True,
        debug_encoding_issues=False
    )
    
    setup_unicode_logging(config)
    logger = logging.getLogger('basic_example')
    
    logger.info("🚀 System starting up...")
    logger.info("✅ Configuration loaded successfully")
    logger.warning("⚠️ This is a warning message")
    logger.error("❌ This is an error message")
    logger.info("🎯 Milestone reached!")

# Example 2: Force ASCII Mode
def example_force_ascii():
    """Force ASCII mode for maximum compatibility."""
    
    config = UnicodeLoggingConfig(
        mode='ascii',
        fallback_on_error=True,
        debug_encoding_issues=False
    )
    
    setup_unicode_logging(config)
    logger = logging.getLogger('ascii_example')
    
    logger.info("🚀 System starting up...")  # Will show as [PHASE]
    logger.info("✅ All systems operational")  # Will show as [OK]
    logger.warning("⚠️ Resource usage high")  # Will show as [WARNING]

# Example 3: Custom Character Mappings for Trading System
def example_trading_mappings():
    """Custom mappings specific to trading/financial applications."""
    
    config = UnicodeLoggingConfig(
        mode='auto',
        fallback_on_error=True,
        custom_mappings={
            # Trading-specific mappings
            '💰': '[PROFIT]',
            '💸': '[LOSS]',
            '📈': '[BULL]',
            '📉': '[BEAR]',
            '⚡': '[FAST]',
            '🎲': '[RANDOM]',
            '🎯': '[TARGET]',
            '🔥': '[HOT]',
            '❄️': '[COLD]',
            '⚖️': '[BALANCE]',
            
            # Portfolio-specific
            '📊': '[PORTFOLIO]',
            '💎': '[PREMIUM]',
            '🏆': '[WINNER]',
            '📋': '[REPORT]',
            '🔍': '[ANALYZE]'
        }
    )
    
    setup_unicode_logging(config)
    logger = logging.getLogger('trading_example')
    
    logger.info("📊 Portfolio analysis started")
    logger.info("💰 Profit target achieved: +15.2%")
    logger.warning("📉 Market downturn detected")
    logger.info("⚖️ Portfolio rebalanced successfully")
    logger.info("🏆 Best performing asset: TECH ETF")

# Example 4: Development/Debug Configuration
def example_debug_mode():
    """Configuration for development and troubleshooting."""
    
    config = UnicodeLoggingConfig(
        mode='auto',
        fallback_on_error=True,
        debug_encoding_issues=True,  # Enable detailed debugging
        custom_mappings={
            '🐛': '[BUG]',
            '🔧': '[FIX]',
            '🧪': '[TEST]',
            '📝': '[LOG]',
            '🔍': '[DEBUG]',
            '💡': '[IDEA]',
            '⚙️': '[CONFIG]',
            '🚨': '[ALERT]'
        }
    )
    
    setup_unicode_logging(config)
    logger = logging.getLogger('debug_example')
    
    logger.debug("🔍 Starting debug session")
    logger.info("🧪 Running test suite")
    logger.warning("🐛 Potential issue detected")
    logger.info("🔧 Applying fix")
    logger.info("💡 Performance optimization idea")

# Example 5: Minimal Configuration
def example_minimal():
    """Minimal configuration with just essential mappings."""
    
    config = UnicodeLoggingConfig(
        mode='auto',
        custom_mappings={
            '✅': '[OK]',
            '❌': '[FAIL]',
            '⚠️': '[WARN]'
        }
    )
    
    setup_unicode_logging(config)
    logger = logging.getLogger('minimal_example')
    
    logger.info("✅ System ready")
    logger.warning("⚠️ Low memory")
    logger.error("❌ Connection failed")

# Example 6: Environment-Specific Configuration
def example_environment_specific():
    """Different configurations based on environment detection."""
    
    import os
    import platform
    
    # Detect environment
    is_windows = platform.system() == 'Windows'
    is_ci = os.environ.get('CI', '').lower() == 'true'
    is_docker = os.path.exists('/.dockerenv')
    
    if is_ci or is_docker:
        # CI/Docker: Use ASCII for reliability
        mode = 'ascii'
        debug = False
    elif is_windows:
        # Windows: Auto-detect with fallback
        mode = 'auto'
        debug = False
    else:
        # Unix-like: Assume Unicode support
        mode = 'unicode'
        debug = False
    
    config = UnicodeLoggingConfig(
        mode=mode,
        fallback_on_error=True,
        debug_encoding_issues=debug,
        custom_mappings={
            '🐳': '[DOCKER]' if is_docker else '[CONTAINER]',
            '🔄': '[CI]' if is_ci else '[BUILD]',
            '🪟': '[WINDOWS]' if is_windows else '[SYSTEM]'
        }
    )
    
    setup_unicode_logging(config)
    logger = logging.getLogger('env_example')
    
    logger.info(f"🪟 Running on {platform.system()}")
    if is_ci:
        logger.info("🔄 CI environment detected")
    if is_docker:
        logger.info("🐳 Docker container detected")

# Example 7: Advanced Custom Mapper
def example_advanced_mapper():
    """Advanced usage with direct CharacterMapper manipulation."""
    
    # Create custom mapper
    mapper = CharacterMapper(mode='auto')
    
    # Add runtime mappings
    mapper.add_mapping('🌟', '[STAR]')
    mapper.add_mapping('🔮', '[PREDICT]')
    mapper.add_mapping('🎪', '[EVENT]')
    
    # Test mappings
    test_message = "🌟 Special event 🎪 with prediction 🔮"
    safe_message = mapper.map_unicode_to_safe(test_message)
    
    print(f"Original: {test_message}")
    print(f"Safe:     {safe_message}")
    
    # Use with logging
    from unicode_logging import EncodingSafeFormatter
    
    formatter = EncodingSafeFormatter(
        character_mapper=mapper,
        fmt='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger('advanced_example')
    handler = logging.StreamHandler()
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    
    logger.info("🌟 Advanced mapper example")
    logger.info("🔮 Prediction: Market will rise")
    logger.info("🎪 Special trading event detected")

# Example 8: Configuration from Environment Variables
def example_env_vars():
    """Load configuration from environment variables."""
    
    import os
    
    # Read from environment
    mode = os.environ.get('UNICODE_LOGGING_MODE', 'auto')
    debug = os.environ.get('UNICODE_LOGGING_DEBUG', 'false').lower() == 'true'
    fallback = os.environ.get('UNICODE_LOGGING_FALLBACK', 'true').lower() == 'true'
    
    config = UnicodeLoggingConfig(
        mode=mode,
        fallback_on_error=fallback,
        debug_encoding_issues=debug
    )
    
    setup_unicode_logging(config)
    logger = logging.getLogger('env_vars_example')
    
    logger.info(f"⚙️ Mode: {mode}")
    logger.info(f"🔧 Debug: {debug}")
    logger.info(f"🛡️ Fallback: {fallback}")

# Example 9: Integration with Existing Logger
def example_existing_logger_integration():
    """Integrate with an existing logging setup."""
    
    # Assume you have an existing logger setup
    logger = logging.getLogger('existing_app')
    logger.setLevel(logging.INFO)
    
    # Add Unicode-safe handler
    from unicode_logging import EncodingSafeFormatter, get_character_mapper
    
    # Get character mapper
    mapper = get_character_mapper(mode='auto')
    
    # Create formatter
    formatter = EncodingSafeFormatter(
        character_mapper=mapper,
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Add to existing handler or create new one
    handler = logging.StreamHandler()
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    # Test logging
    logger.info("🔗 Unicode-safe logging integrated")
    logger.info("✅ Existing logger enhanced")

# Example 10: Performance Testing Configuration
def example_performance_testing():
    """Configuration optimized for performance testing."""
    
    config = UnicodeLoggingConfig(
        mode='ascii',  # Skip encoding detection overhead
        fallback_on_error=False,  # Skip error handling overhead
        debug_encoding_issues=False,  # No debug output
        custom_mappings={
            # Minimal essential mappings only
            '✅': '[OK]',
            '❌': '[FAIL]'
        }
    )
    
    setup_unicode_logging(config)
    logger = logging.getLogger('performance_example')
    
    # Performance test
    import time
    
    start_time = time.time()
    for i in range(1000):
        logger.info(f"✅ Performance test message {i}")
    end_time = time.time()
    
    logger.info(f"⏱️ 1000 messages in {end_time - start_time:.3f} seconds")

# Example 11: Conditional Mapping Based on Log Level
def example_conditional_mapping():
    """Different mappings based on log level or context."""
    
    class ConditionalCharacterMapper(CharacterMapper):
        def get_replacement(self, unicode_char, context=None):
            # Get log level from context if available
            log_level = getattr(context, 'levelname', None) if context else None
            
            # Different mappings for different log levels
            if log_level == 'ERROR':
                error_mappings = {
                    '❌': '[CRITICAL ERROR]',
                    '⚠️': '[ERROR WARNING]',
                    '🚨': '[SYSTEM ALERT]'
                }
                if unicode_char in error_mappings:
                    return error_mappings[unicode_char]
            
            elif log_level == 'WARNING':
                warning_mappings = {
                    '⚠️': '[CAUTION]',
                    '🔔': '[NOTICE]'
                }
                if unicode_char in warning_mappings:
                    return warning_mappings[unicode_char]
            
            # Fall back to default mapping
            return super().get_replacement(unicode_char)
    
    # Use custom mapper
    mapper = ConditionalCharacterMapper(mode='ascii')
    
    from unicode_logging import EncodingSafeFormatter
    
    formatter = EncodingSafeFormatter(character_mapper=mapper)
    
    logger = logging.getLogger('conditional_example')
    handler = logging.StreamHandler()
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    
    logger.info("⚠️ This is an info warning")
    logger.warning("⚠️ This is a warning level message")
    logger.error("❌ This is an error message")

if __name__ == '__main__':
    print("Unicode Logging Configuration Examples")
    print("=" * 50)
    
    examples = [
        ("Basic Auto-Detection", example_basic_auto),
        ("Force ASCII Mode", example_force_ascii),
        ("Trading System Mappings", example_trading_mappings),
        ("Debug Mode", example_debug_mode),
        ("Minimal Configuration", example_minimal),
        ("Environment-Specific", example_environment_specific),
        ("Advanced Mapper", example_advanced_mapper),
        ("Environment Variables", example_env_vars),
        ("Existing Logger Integration", example_existing_logger_integration),
        ("Performance Testing", example_performance_testing),
        ("Conditional Mapping", example_conditional_mapping)
    ]
    
    for name, example_func in examples:
        print(f"\n--- {name} ---")
        try:
            example_func()
            print("✅ Example completed successfully")
        except Exception as e:
            print(f"❌ Example failed: {e}")
        print()