# Design Document

## Overview

The RL Portfolio Rebalancing System is designed as a modular, production-ready solution that leverages the TensorTrade framework's component-based architecture to train and deploy reinforcement learning agents for dynamic portfolio management. The system follows TensorTrade's design principles with fully configurable modules that can be plugged together with minimal restrictions.

The core innovation lies in the integration of realistic trading conditions (transaction costs, slippage) with risk-adjusted reward signals (Sharpe ratio and differential Sharpe ratio optimization) through custom TensorTrade components. The system uses TensorTrade's Stream API for data processing, custom ActionScheme and RewardScheme components with proper component registration, and integrates with Stable Baselines3 for PPO agent training. The continuous action space enables precise portfolio weight adjustments across seven diverse ETFs through TensorTrade's order management system.

## Architecture

The system follows a four-layer architecture designed for modularity, testability, and maintainability:

```mermaid
graph TB
    subgraph "Data Layer"
        A[YFinance Data Fetcher]
        B[Data Preprocessor]
        C[Feature Engineering]
    end
    
    subgraph "TensorTrade Environment Layer"
        D[Portfolio Manager]
        E[Exchange Simulator]
        F[Action Scheme]
        G[Reward Scheme]
        H[Observer]
        I[Data Feed]
    end
    
    subgraph "RL Agent Layer"
        J[PPO Agent]
        K[Policy Network]
        L[Value Network]
    end
    
    subgraph "Evaluation Layer"
        M[Performance Metrics]
        N[Backtesting Engine]
        O[Results Visualization]
    end
    
    A --> B
    B --> C
    C --> I
    I --> H
    D --> E
    E --> F
    F --> G
    G --> J
    J --> K
    J --> L
    J --> M
    M --> N
    N --> O
```

### Layer Responsibilities

1. **Data Layer**: Handles all data acquisition, preprocessing, and feature engineering
2. **TensorTrade Environment Layer**: Implements the trading simulation environment with realistic market conditions
3. **RL Agent Layer**: Contains the PPO agent and neural network components
4. **Evaluation Layer**: Provides comprehensive performance analysis and visualization

## Components and Interfaces

### Data Management Components

#### YFinanceDataFetcher
```python
class YFinanceDataFetcher:
    def __init__(self, window_length_years: int = 4)
    def fetch_etf_data(self, symbols: List[str], window_start: str, window_end: str) -> pd.DataFrame
    def fetch_risk_free_rate(self, window_start: str, window_end: str) -> pd.DataFrame
    def get_current_window_dates(self, reference_date: Optional[str] = None) -> Tuple[str, str]
    def shift_window_forward(self, current_start: str, months: int = 1) -> Tuple[str, str]
    def validate_data_completeness(self, data: pd.DataFrame) -> bool
    def create_price_streams(self, data: pd.DataFrame) -> List[Stream]
```

**Responsibilities:**
- Implement shifting 4-year window data fetching approach
- Fetch monthly OHLCV data for all 7 ETFs within the current window
- Retrieve ^TNX data for risk-free rate calculations within the same window
- Calculate window start and end dates based on current real-time date
- Automatically shift the window forward as time progresses to maintain a rolling 4-year lookback
- Provide methods to shift the window forward for backtesting scenarios
- Handle API rate limits and connection errors
- Validate data completeness and quality within the window
- Create TensorTrade Stream objects from windowed price data

#### DataPreprocessor
```python
class DataPreprocessor:
    def handle_missing_data(self, data: pd.DataFrame) -> pd.DataFrame
    def normalize_features(self, data: pd.DataFrame) -> pd.DataFrame
    def create_returns_series(self, data: pd.DataFrame) -> pd.DataFrame
    def align_timestamps(self, *dataframes: pd.DataFrame) -> List[pd.DataFrame]
    def create_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame
    def create_stream_features(self, data: pd.DataFrame) -> List[Stream]
```

**Responsibilities:**
- Apply forward-fill for missing data
- Normalize price and volume features
- Calculate returns and volatility measures
- Ensure temporal alignment across all assets
- Generate technical indicators using ta library (RSI, MACD, Bollinger Bands, moving averages)
- Create TensorTrade Stream objects for all features

### TensorTrade Environment Components

#### PortfolioWeightActionScheme
```python
class PortfolioWeightActionScheme(TensorTradeActionScheme):
    registered_name = "portfolio-weights"
    def __init__(self, wallets: List[Wallet])
    def action_space(self) -> gym.Space
    def get_orders(self, action: np.ndarray, portfolio: Portfolio) -> List[Order]
    def normalize_weights(self, weights: np.ndarray) -> np.ndarray
    def reset(self) -> None
```

**Key Features:**
- Inherits from TensorTradeActionScheme following TensorTrade's component pattern
- Continuous action space using gym.spaces.Box with 7 dimensions (one per ETF)
- Uses proportion_order() function for calculating rebalancing trades
- Implements registered_name for TensorTrade's component registry
- Automatic weight normalization using softmax to ensure sum equals 1.0

#### SharpeRatioRewardScheme
```python
class SharpeRatioRewardScheme(TensorTradeRewardScheme):
    registered_name = "sharpe-ratio"
    def __init__(self, risk_free_rate_stream: Stream, lookback_window: int = 30)
    def reward(self, env: 'TradingEnv') -> float
    def reset(self) -> None
    def calculate_sharpe_ratio(self, returns: np.ndarray, risk_free_rate: float) -> float
```

**Key Features:**
- Inherits from TensorTradeRewardScheme and implements Component interface
- Uses TensorTrade's Stream API for risk-free rate data integration
- Implements required reward() method that takes TradingEnv as parameter
- Rolling Sharpe ratio calculation with configurable window using Stream operations
- Proper reset() implementation for episode boundaries
- Support for differential Sharpe ratio (DSR) for online optimization

#### TensorTrade Exchange Setup
```python
# Using TensorTrade's built-in Exchange with execute_order service
exchange = Exchange("portfolio_exchange", service=execute_order)(
    Stream.source(vt_prices, dtype="float").rename("USD-VT"),
    Stream.source(iagg_prices, dtype="float").rename("USD-IAGG"),
    Stream.source(reet_prices, dtype="float").rename("USD-REET"),
    Stream.source(gld_prices, dtype="float").rename("USD-GLD"),
    Stream.source(dba_prices, dtype="float").rename("USD-DBA"),
    Stream.source(uso_prices, dtype="float").rename("USD-USO"),
    Stream.source(uup_prices, dtype="float").rename("USD-UUP")
)
```

**Key Features:**
- Uses TensorTrade's built-in Exchange class with simulated execution service
- Stream-based price data integration for each ETF
- Transaction costs and slippage handled through execution service configuration
- Follows TensorTrade's naming convention for trading pairs (USD-SYMBOL)

### RL Agent Components

#### PPOPortfolioAgent
```python
class PPOPortfolioAgent:
    def __init__(self, env: TradingEnv, policy_kwargs: Dict)
    def train(self, total_timesteps: int, callback: BaseCallback = None) -> None
    def predict(self, observation: np.ndarray) -> Tuple[np.ndarray, np.ndarray]
    def save_model(self, path: str) -> None
    def load_model(self, path: str) -> None
```

**Configuration:**
- Policy: MlpPolicy with [256, 256] hidden layers
- Learning rate: 3e-4 with linear decay
- Batch size: 64
- Clip range: 0.2
- Value function coefficient: 0.5
- Entropy coefficient: 0.01

## Data Models

### Market Data Schema
```python
@dataclass
class MarketData:
    timestamp: datetime
    symbol: str
    open: float
    high: float
    low: float
    close: float
    volume: int
    adjusted_close: float
```

### Portfolio State Schema
```python
@dataclass
class PortfolioState:
    timestamp: datetime
    net_worth: float
    cash_balance: float
    asset_values: Dict[str, float]
    asset_weights: Dict[str, float]
    daily_return: float
    cumulative_return: float
```

### Performance Metrics Schema
```python
@dataclass
class PerformanceMetrics:
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    calmar_ratio: float
    win_rate: float
    average_trade_return: float
```

## Error Handling

### Data Fetching Errors
- **Network Failures**: Implement exponential backoff retry mechanism
- **API Rate Limits**: Queue requests with appropriate delays
- **Missing Data**: Use forward-fill with validation warnings
- **Invalid Symbols**: Validate ETF symbols before fetching

### Training Errors
- **Environment Errors**: Comprehensive logging with state snapshots
- **Convergence Issues**: Early stopping and learning rate adjustment
- **Memory Issues**: Batch size reduction and gradient accumulation
- **NaN Values**: Input validation and reward clipping

### Runtime Errors
- **Portfolio Validation**: Ensure weights sum to 1.0 and are non-negative
- **Order Execution**: Handle partial fills and rejected orders
- **Performance Calculation**: Validate metric calculations and handle edge cases

## Testing Strategy

### Unit Testing
- **Data Components**: Mock YFinance API responses, test data preprocessing logic
- **TensorTrade Components**: Test action scheme weight normalization, reward calculation accuracy
- **RL Components**: Test agent initialization, policy network architecture
- **Performance Components**: Test metric calculations with known datasets

### Integration Testing
- **Environment Integration**: Test complete TensorTrade environment setup and stepping
- **Data Pipeline**: Test end-to-end data flow from fetching to environment observations
- **Training Pipeline**: Test complete training loop with synthetic data
- **Evaluation Pipeline**: Test backtesting with historical data

### Performance Testing
- **Training Speed**: Benchmark training time with different batch sizes and network architectures
- **Memory Usage**: Monitor memory consumption during training and evaluation
- **Data Loading**: Test data fetching performance with large date ranges
- **Scalability**: Test system performance with additional assets

### Validation Testing
- **Financial Accuracy**: Validate portfolio calculations against manual computations
- **Reward Signal**: Test Sharpe ratio calculations against financial libraries
- **Transaction Costs**: Verify cost calculations match expected values
- **Risk Metrics**: Validate drawdown and volatility calculations

## Configuration Management

### Environment Configuration
```yaml
data:
  window_length_years: 4  # Fixed 4-year window length
  window_shift_months: 1  # Shift window forward by 1 month for backtesting
  reference_date: null  # Use current date if null, or specify for backtesting
  real_time_alignment: true  # Window shifts automatically with real-time progression
  etf_symbols: ["VT", "IAGG", "REET", "GLD", "DBA", "USO", "UUP"]
  risk_free_symbol: "^TNX"
  frequency: "monthly"  # Monthly rebalancing frequency
  
trading:
  transaction_cost: 0.001
  slippage_range: [0.0, 0.01]
  initial_cash: 100000
  
tensortrade:
  window_size: 12  # 12 months of historical data
  max_allowed_loss: 0.6
  enable_logger: true
  
training:
  total_timesteps: 50000
  learning_rate: 0.0003
  batch_size: 64
  policy_layers: [256, 256]
  
evaluation:
  sharpe_window: 12  # 12 months for Sharpe calculation
  benchmark_strategy: "equal_weight"
  reward_scheme: "sharpe-ratio"  # or "differential-sharpe"
```

### Hyperparameter Optimization
- Grid search over learning rates: [1e-4, 3e-4, 1e-3]
- Network architectures: [[128, 128], [256, 256], [512, 256]]
- Reward function parameters: Sharpe window sizes [20, 30, 60]
- Action space constraints: minimum trade thresholds [0.01, 0.05, 0.1]

## Deployment Considerations

### Model Persistence
- Save trained models in standardized format (pickle/joblib)
- Version control for model artifacts
- Model metadata tracking (training date, performance metrics, hyperparameters)

### Monitoring and Logging
- Training progress monitoring with TensorBoard integration
- Performance metric tracking over time
- Alert system for significant performance degradation
- Comprehensive audit trail for all trading decisions

### Scalability
- Modular design allows easy addition of new assets
- Configurable training parameters for different market conditions
- Support for distributed training with Ray/Dask
- Database integration for large-scale data storage