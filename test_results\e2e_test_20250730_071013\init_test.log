2025-07-30 07:10:13 - e2e_unicode_test - INFO - setup_logging:129 - Logging to rotating file: test_results\e2e_test_20250730_071013\init_test.log (max 50MB, 5 backups)
2025-07-30 07:10:13 - e2e_unicode_test - INFO - detect_console_encoding:115 - Encoding detection completed: utf-8 (UTF-8: True, Unicode: True, Recommended: unicode, Confidence: 0.90)
[OK] Unicode-safe logging initialized successfully
   Effective mode: auto
   Console encoding: utf-8
   Character mappings: 166
ComprehensiveLogger initialized with Unicode-safe logging
[PHASE] System initialization test
[OK] Unicode logging enabled
[WARNING] Test warning message
[FAIL] Test error message
