# Product Overview

## RL Portfolio Rebalancing System

A comprehensive reinforcement learning based portfolio rebalancing system using the TensorTrade framework. The system trains a PPO agent to dynamically optimize portfolio allocations across seven ETFs on a monthly basis to maximize risk-adjusted returns.

### Core Purpose
- **Dynamic Portfolio Management**: Automated rebalancing across 7 diverse ETFs (VT, IAGG, REET, GLD, DBA, USO, UUP)
- **Risk-Adjusted Optimization**: Uses Sharpe ratio and differential Sharpe ratio reward schemes
- **Realistic Trading Conditions**: Models transaction costs, slippage, and market impact

### Key Features
- Reinforcement learning with PPO algorithm and TensorTrade framework
- Shifting 4-year window approach for consistent training and evaluation periods
- Technical analysis integration (RSI, MACD, Bollinger Bands, moving averages)
- Comprehensive performance evaluation and backtesting capabilities
- Monthly rebalancing frequency with continuous action space for precise weight allocation

### Target Users
Quantitative traders and portfolio managers who need an automated, adaptive portfolio management solution that can learn from market dynamics and adjust allocations based on changing market conditions.

### Educational Focus
This project is designed for educational and research purposes in quantitative finance and reinforcement learning, demonstrating the application of deep reinforcement learning to real-world portfolio optimization problems.