# Technology Stack

## Core Technologies

### Python Environment
- **Python 3.8+** required
- Virtual environment recommended for dependency isolation

### Key Libraries & Frameworks

#### Data & Financial Analysis
- **pandas** (>=1.5.0): Data manipulation and analysis
- **numpy** (>=1.21.0): Numerical computing
- **yfinance** (>=0.2.0): Financial data fetching from Yahoo Finance
- **ta** (>=0.10.0): Technical analysis indicators library

#### Reinforcement Learning
- **TensorTrade**: Trading environment framework (install from GitHub)
- **stable-baselines3** (>=2.0.0): PPO algorithm implementation
- **gym** (>=0.21.0): RL environment interface

#### Visualization & Analysis
- **matplotlib** (>=3.5.0): Basic plotting
- **seaborn** (>=0.11.0): Statistical visualization
- **scikit-learn** (>=1.1.0): ML utilities
- **scipy** (>=1.9.0): Scientific computing

## Installation & Setup

### Automatic Installation
```bash
python install_dependencies.py
```

### Manual Installation
```bash
# Install standard packages
pip install -r requirements.txt

# Install TensorTrade from GitHub (required)
pip install git+https://github.com/tensortrade-org/tensortrade.git
```

### Dependency Verification
The system includes built-in dependency verification that runs automatically and reports missing packages.

## Common Commands

### Running the System
```bash
python rl_portfolio_rebalancing.py
```

### Project Structure Commands
- Main system: `rl_portfolio_rebalancing.py`
- Configuration: `config.py`
- Dependencies: `requirements.txt` and `install_dependencies.py`

## Architecture Notes

### TensorTrade Integration
- Uses TensorTrade's component-based architecture
- Custom ActionScheme and RewardScheme classes with proper component registration
- Stream-based data processing and feature engineering
- Realistic order execution with transaction costs

### Data Management
- Shifting 4-year window approach for consistent training periods
- Window moves forward through time for backtesting and retraining
- Configurable window length and shift intervals
- Automatic date range calculation based on reference dates

### Configuration Management
All system settings are centralized in `config.py` with sections for:
- Data configuration (ETF symbols, window parameters, shift intervals)
- Trading configuration (costs, slippage, initial cash)
- Training configuration (PPO hyperparameters)
- Technical indicators configuration
- Logging and directory settings